#!/usr/bin/env zsh
# Enhanced Directory Navigation with Arrow Keys + Vim Keys
# Combines dirhistory functionality with vim-style navigation

# === DIRECTORY HISTORY SETUP ===
dirhistory_past=($PWD)
dirhistory_future=()
export dirhistory_past
export dirhistory_future
export DIRHISTORY_SIZE=30

# === DIRECTORY HISTORY FUNCTIONS ===

# Pop the last element of dirhistory_past
dirhistory_pop_past() {
  if [[ $# -eq 0 ]]; then
    return 1
  fi

  if [[ ${#dirhistory_past} -eq 0 ]]; then
    return 1
  fi

  eval "$1='${dirhistory_past[$#dirhistory_past]}'"
  dirhistory_past[$#dirhistory_past]=()

  if [[ ${#dirhistory_past} -gt $DIRHISTORY_SIZE ]]; then
    dirhistory_past[1]=()
  fi
}

# Pop the last element of dirhistory_future
dirhistory_pop_future() {
  if [[ $# -eq 0 ]]; then
    return 1
  fi

  if [[ ${#dirhistory_future} -eq 0 ]]; then
    return 1
  fi

  eval "$1='${dirhistory_future[$#dirhistory_future]}'"
  dirhistory_future[$#dirhistory_future]=()
}

# Add directory to history
dirhistory_add() {
  if [[ $# -eq 0 ]]; then
    return 1
  fi

  dirhistory_past+=($1)
}

# Navigate back in directory history
dirhistory_back() {
  local cwd=$PWD
  local d=""

  # Last element in dirhistory_past is the cwd.
  dirhistory_pop_past d
  if [[ $? -eq 0 ]]; then
    dirhistory_future+=($cwd)
    cd $d
  fi
  zle && zle reset-prompt
}

# Navigate forward in directory history
dirhistory_forward() {
  local d=""

  dirhistory_pop_future d
  if [[ $? -eq 0 ]]; then
    dirhistory_past+=($PWD)
    cd $d
  fi
  zle && zle reset-prompt
}

# Navigate up in directory hierarchy
dirhistory_up() {
  dirhistory_past+=($PWD)
  cd ..
  zle && zle reset-prompt
}

# Navigate down into first directory
dirhistory_down() {
  dirhistory_past+=($PWD)

  # Find first directory in current location
  local first_dir=$(find . -maxdepth 1 -type d ! -name '.' | head -1)
  if [[ -n "$first_dir" ]]; then
    cd "$first_dir"
  fi
  zle && zle reset-prompt
}

# === ENHANCED NAVIGATION FUNCTIONS ===

# Smart directory navigation with FZF
smart_cd() {
  local dir
  if [[ $# -eq 0 ]]; then
    # No arguments - use FZF to select directory
    dir=$(fd --type d --hidden --exclude .git --exclude node_modules --exclude .cache 2>/dev/null | \
      fzf --height 60% --reverse --border \
          --prompt="📁 Navigate: " \
          --preview='eza --icons -la {} 2>/dev/null || ls -la {}' \
          --preview-window=right:50%)
  else
    dir="$1"
  fi

  if [[ -n "$dir" && -d "$dir" ]]; then
    dirhistory_past+=($PWD)
    cd "$dir"
    zle && zle reset-prompt
  fi
}

# Quick parent directory navigation
up() {
  local levels=${1:-1}
  local path=""

  for ((i=1; i<=levels; i++)); do
    path="../$path"
  done

  dirhistory_past+=($PWD)
  cd "$path"
  zle && zle reset-prompt
}

# === ZLE WIDGET SETUP ===
zle -N dirhistory_back
zle -N dirhistory_forward
zle -N dirhistory_up
zle -N dirhistory_down
zle -N smart_cd

# === KEY BINDINGS ===

# Original arrow key bindings
bindkey '^[[1;3D' dirhistory_back      # Alt+Left
bindkey '^[[1;3C' dirhistory_forward   # Alt+Right
bindkey '^[[1;3A' dirhistory_up        # Alt+Up
bindkey '^[[1;3B' dirhistory_down      # Alt+Down

# Enhanced vim-style bindings with modifiers
bindkey '^[h' dirhistory_back          # Alt+h - back in history
bindkey '^[l' dirhistory_forward       # Alt+l - forward in history
bindkey '^[k' dirhistory_up            # Alt+k - up directory
bindkey '^[j' dirhistory_down          # Alt+j - down into first dir

# Ctrl+Alt combinations for power users
bindkey '^[^H' dirhistory_back         # Ctrl+Alt+h
bindkey '^[^L' dirhistory_forward      # Ctrl+Alt+l
bindkey '^[^K' dirhistory_up           # Ctrl+Alt+k
bindkey '^[^J' dirhistory_down         # Ctrl+Alt+j

# Smart CD with Ctrl+G
bindkey '^G' smart_cd                  # Ctrl+G - FZF directory navigation

# === WORD DELETION ENHANCEMENTS ===

# Precise word deletion (stops at punctuation)
precise-backward-delete-word() {
  local WORDCHARS='*?_-.[]~=/&;!#$%^(){}<>'
  zle backward-delete-word
}

# Fast word deletion (jumps over punctuation)
fast-backward-delete-word() {
  local WORDCHARS=''
  zle backward-delete-word
}

# Setup ZLE widgets for word deletion
zle -N precise-backward-delete-word
zle -N fast-backward-delete-word

# Key bindings for word deletion
# Normal backspace should delete single characters (default behavior)
bindkey "^?" backward-delete-char              # Backspace - single character (normal)
bindkey "^H" precise-backward-delete-word      # Ctrl+Backspace - precise word deletion
bindkey "^W" fast-backward-delete-word         # Ctrl+W - fast word deletion
bindkey '^[[3;2~' fast-backward-delete-word    # Shift+Backspace - fast word deletion

# Alternative bindings for different terminals
bindkey '^[^?' fast-backward-delete-word       # Alt+Backspace - fast (fallback)

# === DIRECTORY CHANGE HOOK ===
# Update history when changing directories
chpwd() {
  # Add current directory to history when changing
  if [[ ${dirhistory_past[-1]} != $PWD ]]; then
    dirhistory_past+=($PWD)

    # Limit history size
    if [[ ${#dirhistory_past} -gt $DIRHISTORY_SIZE ]]; then
      dirhistory_past[1]=()
    fi

    # Clear future when making a new path
    dirhistory_future=()
  fi

  # Call other chpwd functions if they exist
  if [[ -n $chpwd_functions ]]; then
    for func in $chpwd_functions; do
      if [[ $func != "chpwd" ]]; then
        $func
      fi
    done
  fi
}

# === ALIASES FOR CONVENIENCE ===
alias ..='up 1'
alias ...='up 2'
alias ....='up 3'
alias .....='up 4'

alias back='dirhistory_back'
alias forward='dirhistory_forward'
alias cdd='smart_cd'

# === DIRECTORY BOOKMARKS ===
typeset -A dir_bookmarks

# Bookmark current directory
bookmark() {
  if [[ $# -eq 0 ]]; then
    echo "Usage: bookmark <name>"
    return 1
  fi

  dir_bookmarks[$1]=$PWD
  echo "Bookmarked $PWD as '$1'"
}

# Jump to bookmark
jump() {
  if [[ $# -eq 0 ]]; then
    # Show all bookmarks with FZF
    local selection
    selection=$(printf '%s -> %s\n' ${(kv)dir_bookmarks} | \
      fzf --height 60% --reverse --border \
          --prompt="🔖 Jump to: " \
          --preview='ls -la {2}' \
          --delimiter=' -> ' \
          --with-nth=1)

    if [[ -n "$selection" ]]; then
      local bookmark_name=$(echo "$selection" | cut -d' ' -f1)
      local bookmark_path=${dir_bookmarks[$bookmark_name]}
      if [[ -d "$bookmark_path" ]]; then
        dirhistory_past+=($PWD)
        cd "$bookmark_path"
      fi
    fi
  else
    local bookmark_path=${dir_bookmarks[$1]}
    if [[ -d "$bookmark_path" ]]; then
      dirhistory_past+=($PWD)
      cd "$bookmark_path"
    else
      echo "Bookmark '$1' not found"
    fi
  fi
}

# List bookmarks
bookmarks() {
  if [[ ${#dir_bookmarks} -eq 0 ]]; then
    echo "No bookmarks set"
    return
  fi

  echo "📁 Directory Bookmarks:"
  for name path in ${(kv)dir_bookmarks}; do
    echo "  $name -> $path"
  done
}

# Remove bookmark
unbookmark() {
  if [[ $# -eq 0 ]]; then
    echo "Usage: unbookmark <name>"
    return 1
  fi

  if [[ -n ${dir_bookmarks[$1]} ]]; then
    unset "dir_bookmarks[$1]"
    echo "Removed bookmark '$1'"
  else
    echo "Bookmark '$1' not found"
  fi
}

# === PERFORMANCE OPTIMIZATIONS ===
# Functions are defined in this file, no need for autoload

# Initialize with current directory
dirhistory_past=($PWD)
