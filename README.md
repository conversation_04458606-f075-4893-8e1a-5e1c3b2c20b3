# 🚀 Optimized Zsh Configuration

High-performance, feature-rich Zsh setup with seamless Zellij, FZF, and development tool integration.

## ⚡ Performance Features

- **Instant Prompt**: Sub-second startup with Powerlevel10k instant prompt
- **Smart Caching**: Optimized plugin loading and completion caching
- **No Duplicate Loading**: Fixed duplicate sourcing issues
- **Lazy Loading**: Heavy functions loaded only when needed

## 📁 File Structure

### Core Configuration
- `~/.zshrc` - Main configuration file with optimized sourcing
- `00-environment.zsh` - Environment variables and API keys
- `01-performance.zsh` - Performance optimizations and caching
- `01-xdg.zsh` - XDG Base Directory compliance

### Shell Features
- `02-history.zsh` - History configuration
- `03-completion.zsh` - Completion settings
- `04-functions.zsh` - Custom shell functions
- `05-fzf.zsh` - FZF integration and configuration

### Tool Integration
- `06-atuin.zsh` - Advanced shell history with Atuin
- `07-zinit.zsh` - Plugin manager with optimized loading
- `08-prompt.zsh` - Prompt configuration (minimal, delegates to .zshrc)

### User Interface
- `09-atuin-keybindings.zsh` - Atuin key bindings
- `11-vi-mode-cursor.zsh` - Vi mode cursor styling
- `12-vi-mode-keybindings.zsh` - Vi mode key bindings
- `13-eza-aliases.zsh` - Modern ls replacement aliases
- `14-aliases-optimized.zsh` - **NEW**: Comprehensive alias system with FZF integration

### Prompt & Styling
- `.p10k.zsh` - Main Powerlevel10k configuration (1751 lines)
- `p10k-optimized.zsh` - Performance optimizations for P10k

## 🎯 Key Features

### 🔥 Performance Optimizations
- **Sub-second startup**: Optimized plugin loading and instant prompt
- **Smart caching**: Completion and git status caching
- **Lazy loading**: Functions loaded only when needed
- **No duplicates**: Fixed duplicate sourcing that was slowing startup

### 🛠️ Development Tools
- **Zinit**: Fast plugin manager with SSH-based loading
- **FZF**: Fuzzy finding for files, directories, history, and aliases
- **Atuin**: Advanced shell history with sync and search
- **Eza**: Modern ls replacement with icons and colors
- **Git integration**: Smart git aliases and status in prompt

### 🖥️ Terminal Experience
- **Zellij integration**: Smart session management and shortcuts
- **Vi mode**: Full vi key bindings with visual cursor feedback
- **Powerlevel10k**: Beautiful, fast prompt with git status
- **Smart aliases**: FZF-powered alias discovery (Ctrl+A)

### 🚀 Quick Start Shortcuts
- `Ctrl+A` - Interactive alias finder
- `Ctrl+R` - Atuin history search
- `cdf` - FZF directory navigation
- `proj` - Project switcher
- `zjs` - Smart Zellij session management
- `alias_help` - Show all available shortcuts

## 🔧 Installation & Usage

### Prerequisites
```bash
# Install required tools
yay -S zsh zinit powerlevel10k-git atuin fzf eza bat fd zellij
```

### Quick Setup
```bash
# Backup existing config
cp ~/.zshrc ~/.zshrc.backup

# The configuration is already in ~/.config/zsh/
# Just reload zsh to apply changes
exec zsh
```

### Performance Testing
```bash
# Test startup time (should be < 0.1s)
time zsh -i -c exit

# Test with foot terminal (Hyprland integration)
time foot zsh -c "echo 'Ready'; exit"
```

## 🎮 Usage Guide

### Alias Management
- **Ctrl+A**: Interactive alias finder with FZF
- **alias_help**: Show comprehensive help
- All aliases are organized by category (System, Git, Zellij, etc.)

### Zellij Integration
- **zjs**: Smart attach to existing session or create new
- **zj/zja/zjn**: Quick Zellij shortcuts
- **zjl/zjk**: List/kill sessions

### Development Workflow
- **proj**: Switch between projects with FZF
- **cdf**: Navigate directories with preview
- **gs/ga/gc/gp**: Git workflow shortcuts
- **py/venv/activate**: Python development shortcuts

### System Management
- **sysup**: Update system packages
- **sysclean**: Clean package cache
- **sysinfo**: Show system information
- **reload**: Reload zsh configuration

## 🔍 Troubleshooting

### Slow Startup
- Check `time zsh -i -c exit` (should be < 0.1s)
- Run `p10k-clean` if prompt issues occur
- Use `p10k-rebuild` to rebuild cache

### Zellij Issues
- Configuration updated to fix ScrollLeft/ScrollRight errors
- Use `zjs` for smart session management

### Plugin Issues
- Zinit plugins load via SSH - ensure SSH keys are configured
- Silent error handling prevents console output during init

## 📊 Performance Metrics

| Component | Startup Impact | Reliability | Features |
|-----------|----------------|-------------|----------|
| P10k Instant Prompt | ⚡⚡⚡⚡⚡ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Zinit Plugins | ⚡⚡⚡⚡ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| FZF Integration | ⚡⚡⚡⚡⚡ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Atuin History | ⚡⚡⚡ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Custom Functions | ⚡⚡⚡⚡ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 🔄 Recent Optimizations

- ✅ Fixed duplicate file sourcing (major performance gain)
- ✅ Removed console output during initialization
- ✅ Optimized P10k configuration for speed
- ✅ Fixed Zellij configuration errors
- ✅ Added comprehensive alias management system
- ✅ Consolidated P10k files (removed redundant configs)
- ✅ Enhanced FZF integration throughout

---

*Configuration optimized for speed, reliability, and seamless terminal workflow with Zellij, FZF, and modern development tools.*
