# Vi Mode Cursor Configuration
# Set a thin cursor in insert mode and block cursor in command mode
zle-keymap-select () {
    if [[ $KEYMAP = vicmd ]]; then
        # Command mode - block cursor
        echo -ne '\e[1 q'
    elif [[ $KEYMAP = main ]] || [[ $KEYMAP = viins ]]; then
        # Insert mode - thin cursor
        echo -ne '\e[5 q'
    fi
}
zle -N zle-keymap-select

# Initialize cursor shape on zsh startup
# Start with a thin cursor for insert mode
echo -ne '\e[5 q'
