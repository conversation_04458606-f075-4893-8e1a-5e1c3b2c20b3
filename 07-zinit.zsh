# --- Z<PERSON>t Plugin Manager ---

# Enable vim mode (vi keybindings)
bindkey -v

# ZINIT_HOME should point to the base directory for zinit.
# Zinit's own repository will be cloned into $ZINIT_HOME/zinit.git.
ZINIT_HOME="${XDG_DATA_HOME:-${HOME}/.local/share}/zinit"
ZINIT_GIT_DIR="${ZINIT_HOME}/zinit.git" # Path to zinit's own git repository

# Check if zinit.zsh is in its git directory, if not, clone zinit
if [ ! -f "${ZINIT_GIT_DIR}/zinit.zsh" ]; then
  echo "zinit.zsh not found in ${ZINIT_HOME}. Attempting to clone zinit..."
  # Ensure parent directory for ZINIT_HOME exists (e.g., ~/.local/share)
  mkdir -p "$(dirname "$ZINIT_HOME")"
  # Ensure ZINIT_HOME itself exists (e.g. ~/.local/share/zinit)
  mkdir -p "$ZINIT_HOME"
  # Remove potentially incomplete zinit git directory before cloning
  rm -rf "${ZINIT_GIT_DIR}" 2>/dev/null
  <NAME_EMAIL>:zdharma-continuum/zinit.git "${ZINIT_GIT_DIR}" 2>/dev/null
  # Silent installation - no console output during init
fi

# Source zinit only if the file exists in its git directory
if [ -f "${ZINIT_GIT_DIR}/zinit.zsh" ]; then
  source "${ZINIT_GIT_DIR}/zinit.zsh"

  # Load plugins with zinit (using SSH URLs)
  zinit ice git"**************:zsh-users/zsh-autosuggestions.git"
  zinit light zsh-users/zsh-autosuggestions

  zinit ice git"**************:Aloxaf/fzf-tab.git"
  zinit light Aloxaf/fzf-tab # For fzf-powered tab completions
  # yazi: Fast terminal file manager written in Rust, with Zsh integration for cd.
  # Requires: yazi (e.g., yay -S yazi)
  zinit ice lucid as"plugin" git"**************:yazi-rs/yazi.git" && zinit light yazi-rs/yazi

  # zsh-syntax-highlighting: Fish-like syntax highlighting for Zsh. Must be last.
  # Using lucid, as"plugin", depth"1" and explicit SSH URL for zsh-users/zsh-syntax-highlighting
  zinit ice lucid as"plugin" depth"1" git"**************:zsh-users/zsh-syntax-highlighting.git" && \
  zinit light zsh-users/zsh-syntax-highlighting

  # --- Oh My Zsh (OMZ) Plugins via Zinit ---
  # For plugins with external dependencies, install them via: yay -S <package-name>

  # Load OMZ libraries that many plugins depend on
  zinit snippet OMZ::lib/git.zsh
  zinit snippet OMZ::lib/completion.zsh
  zinit snippet OMZ::lib/key-bindings.zsh

  # --- Simple plugins (single file) ---
  # archlinux: Aliases for pacman/yay
  zinit snippet OMZ::plugins/archlinux/archlinux.plugin.zsh

  # dirhistory: Navigate directory history (Alt+Left/Right/Up)
  zinit snippet OMZ::plugins/dirhistory/dirhistory.plugin.zsh

  # docker-compose: Aliases for docker-compose
  zinit snippet OMZ::plugins/docker-compose/docker-compose.plugin.zsh

  # dotenv: Autoload .env files
  # zinit snippet OMZ::plugins/dotenv/dotenv.plugin.zsh

  # common-aliases: Many common aliases
  zinit snippet OMZ::plugins/common-aliases/common-aliases.plugin.zsh

  # copypath: Copies current path to clipboard
  zinit snippet OMZ::plugins/copypath/copypath.plugin.zsh

  # cp: Aliases cp to 'cp -i' (interactive)
  zinit snippet OMZ::plugins/cp/cp.plugin.zsh

  # frontend-search: Search npm, bower, etc. (fs query)
  zinit snippet OMZ::plugins/frontend-search/frontend-search.plugin.zsh

  # git-auto-fetch: Auto-fetches in git repos
  zinit snippet OMZ::plugins/git-auto-fetch/git-auto-fetch.plugin.zsh

  # git-commit: Alias 'gc' for 'git commit'
  zinit snippet OMZ::plugins/git-commit/git-commit.plugin.zsh

  # --- Complex plugins (require entire directory) ---
  # Use direct GitHub repository paths to download the entire plugin directory

  # eza: Modern ls replacement  # eza: Replaces ls with eza. Requires: eza
  # yay -S eza
  zinit snippet OMZ::plugins/eza/eza.plugin.zsh

  # gh: GitHub CLI. Requires: github-cli
  # yay -S github-cli
  zinit ice lucid as"plugin" ver"master" git"**************:cli/cli.git"
  zinit light cli/cli

  # git-flow: Git Flow AVH Edition. Requires: git-flow-avh
  # yay -S git-flow-avh
  zinit ice lucid as"plugin" from"gh" pick"plugins/git-flow/git-flow.plugin.zsh" && zinit light ohmyzsh/ohmyzsh

  # autojump: Jump to frequently used directories. Requires: autojump
  # yay -S autojump
  zinit snippet OMZ::plugins/autojump/autojump.plugin.zsh

  # bun: Aliases for Bun. Requires: bun-bin
  # yay -S bun-bin
  zinit ice lucid as"plugin" && zinit light ntnyq/omz-plugin-bun

  # catimg: Prints images to terminal. Requires: catimg
  # yay -S catimg
  zinit snippet OMZ::plugins/catimg/catimg.plugin.zsh

  # colorize: Colorizes output of various commands. Requires: python-pygments
  # yay -S python-pygments
  zinit snippet OMZ::plugins/colorize/colorize.plugin.zsh

  # colored-man-pages: Colorizes man pages. Requires: less
  zinit snippet OMZ::plugins/colored-man-pages/colored-man-pages.plugin.zsh

  # jsontools: Utilities for JSON
  zinit snippet OMZ::plugins/jsontools/jsontools.plugin.zsh

  # foot: Aliases for foot terminal
  zinit snippet OMZ::plugins/foot/foot.plugin.zsh

  # gcloud: Google Cloud SDK completions/aliases. Requires: google-cloud-sdk
  # Install Google Cloud SDK from AUR or official docs. Then configure with 'gcloud init'.
  # zinit snippet OMZ::plugins/gcloud/gcloud.plugin.zsh

  # ipfs: IPFS helper functions. Requires: go-ipfs
  # yay -S go-ipfs
  zinit ice lucid as"plugin" from"gh" pick"plugins/ipfs/ipfs.plugin.zsh" && zinit light ohmyzsh/ohmyzsh

  # jira: JIRA CLI integration. Requires: jira-cli (e.g., go-jira from AUR)
  # yay -S go-jira
  zinit snippet OMZ::plugins/jira/jira.plugin.zsh

  # web-search: Search the web from CLI. Uses $BROWSER
  zinit snippet OMZ::plugins/web-search/web-search.plugin.zsh

  # yarn: Yarn package manager aliases. Requires: yarn
  # yay -S yarn
  zinit snippet OMZ::plugins/yarn/yarn.plugin.zsh

  # vscode: VSCode aliases (code, c). Requires: visual-studio-code-bin
  # yay -S visual-studio-code-bin
  zinit snippet OMZ::plugins/vscode/vscode.plugin.zsh

  # vault: Aliases  # vault: HashiCorp Vault helpers. Requires: vault
  # yay -S vault
  zinit snippet OMZ::plugins/vault/vault.plugin.zsh

  # uv: Aliases  # uv: uv (Python package manager) aliases. Requires: uv
  # yay -S uv
  zinit ice from"gh-r" as"plugin" lucid && zinit snippet OMZ::plugins/uv/uv.plugin.zsh

  # toolbox: Fedora Toolbox helpers. Requires: toolbox
  # sudo pacman -S toolbox # If on Arch and using it
  zinit snippet OMZ::plugins/toolbox/toolbox.plugin.zsh

  # torrent: Aliases for torrent clients
  zinit snippet OMZ::plugins/torrent/torrent.plugin.zsh

  # transfer: Transfer files using transfer.sh. Requires: curl
  zinit snippet OMZ::plugins/transfer/transfer.plugin.zsh

  # ufw: Aliases for Uncomplicated  # ufw: UFW firewall helpers. Requires: ufw
  # yay -S ufw
  zinit ice lucid as"plugin" from"gh" pick"plugins/ufw/ufw.plugin.zsh" && zinit light ohmyzsh/ohmyzsh

  # wd: Warp directory - name directories and jump to them. Synergizes with autojump.
  zinit ice from"gh-r" as"plugin" lucid atload'!source wd.sh' && zinit snippet OMZ::plugins/wd/wd.plugin.zsh

  # systemd: Systemd helpers. Requires: systemd
  zinit snippet OMZ::plugins/systemd/systemd.plugin.zsh

  # systemadmin: System administration aliases.
  zinit snippet OMZ::plugins/systemadmin/systemadmin.plugin.zsh

  # stripe:  # torrent: Torrent helpers. Requires: transmission-cli
  # yay -S transmission-cli
  zinit snippet OMZ::plugins/torrent/torrent.plugin.zsh

  # safe-paste: Prevents pasting commands with leading spaces.
  zinit snippet OMZ::plugins/safe-paste/safe-paste.plugin.zsh

  # rust: Aliases for Rust development. Requires: rustup (Rust toolchain)
  # yay -S rustup; rustup-init
  zinit ice from"gh-r" as"plugin" lucid && zinit snippet OMZ::plugins/rust/rust.plugin.zsh

  # rsync: Aliases for rsync
  zinit snippet OMZ::plugins/rsync/rsync.plugin.zsh

  # pj: Jump to projects in ~/Projects or ~/Documents/Projects
  # (Uses ~/Documents/  # pj: Project jump. Requires: pj (custom or standard)
  zinit snippet OMZ::plugins/pj/pj.plugin.zsh
  # --- Configure zsh-autosuggestions ---
  # A muted grey (e.g., color 244) is often good for suggestions.
  ZSH_AUTOSUGGEST_HIGHLIGHT_STYLE='fg=244' # Muted grey for suggestions

  # --- Configure fzf-tab (Aloxaf/fzf-tab) ---
  # https://github.com/Aloxaf/fzf-tab
  zstyle ':fzf-tab:*' fzf-command /usr/bin/fzf --height 80% --reverse --ansi --border --prompt='❯ '
  zstyle ':completion:*:descriptions' format '[%d]'
  zstyle ':completion:*' menu select
  # Rich preview with icons for fzf-tab
  zstyle ':fzf-tab:complete:*:*' fzf-preview 'bat --style=numbers --color=always {} 2>/dev/null || exa --icons -l {} 2>/dev/null || ls -la {}'
  zstyle ':fzf-tab:complete:*:*' fzf-flags --preview-window=right:60%

fi
# Note: Zinit error messages removed to prevent console output during init
# --- End Zinit Plugin Manager ---
