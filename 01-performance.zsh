# Performance Optimizations for Zsh
# This file should be sourced early in the configuration process

# Enable caching for completion data
zstyle ':completion:*' use-cache on
zstyle ':completion:*' cache-path "${XDG_CACHE_HOME:-$HOME/.cache}/zsh/zcompcache"

# Optimize plugin loading with zinit (if available)
if command -v zinit >/dev/null 2>&1; then
  zinit light-mode for \
    zdharma-continuum/fast-syntax-highlighting \
    zsh-users/zsh-autosuggestions \
    zsh-users/zsh-completions
fi

# Disable unnecessary checks for faster startup
unsetopt CHECK_JOBS
unsetopt AUTO_CD

# Limit the number of history entries to save/load for faster access
HISTSIZE=1000
SAVEHIST=1000

# Use a dedicated cache directory for Zsh
ZSH_CACHE_DIR="${XDG_CACHE_HOME:-$HOME/.cache}/zsh"
mkdir -p "$ZSH_CACHE_DIR"
