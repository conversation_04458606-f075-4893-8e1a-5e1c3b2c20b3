# Custom aliases for eza (modern ls replacement)
# Override any problematic aliases from OMZ plugin to avoid errors like 'Flag -<PERSON> cannot take a value'

if command -v eza >/dev/null 2>&1; then
  alias l='eza -la --icons --group-directories-first'
  alias ls='eza --icons --group-directories-first'
  alias ll='eza -l --icons --group-directories-first'
  alias la='eza -la --icons --group-directories-first'
  alias lt='eza -laT --icons --group-directories-first'
fi
