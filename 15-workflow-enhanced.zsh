#!/usr/bin/env zsh
# Enhanced Workflow Functions for Zellij/Zsh/Hyprland Integration
# Optimized for keyboard-driven development workflow

# === FZF-TAB CONFIGURATION ===
# Ensure fzf-tab works properly
zstyle ':fzf-tab:*' fzf-command fzf
zstyle ':fzf-tab:*' fzf-bindings 'tab:accept'
zstyle ':fzf-tab:*' accept-line enter
zstyle ':fzf-tab:*' continuous-trigger '/'
zstyle ':fzf-tab:*' fzf-preview 'bat --style=numbers --color=always $realpath 2>/dev/null || eza --icons -la $realpath 2>/dev/null || ls -la $realpath'

# === ENHANCED FZF FUNCTIONS ===

# FZF File Navigation with Preview
fzf_file() {
  local file
  file=$(fd --type f --hidden --exclude .git --exclude node_modules --exclude .cache 2>/dev/null | \
    fzf --height 80% --reverse --border \
        --prompt="📄 Open File: " \
        --preview='bat --style=numbers --color=always {} 2>/dev/null || cat {} 2>/dev/null || echo "Binary file"' \
        --preview-window=right:60% \
        --bind='enter:accept,esc:cancel,ctrl-y:execute-silent(echo {} | wl-copy)')

  if [[ -n "$file" ]]; then
    ${EDITOR:-nvim} "$file"
  fi
}

# FZF Directory Navigation with Actions
fzf_dir() {
  local dir
  dir=$(fd --type d --hidden --exclude .git --exclude node_modules --exclude .cache 2>/dev/null | \
    fzf --height 80% --reverse --border \
        --prompt="📁 Navigate: " \
        --preview='eza --icons -la {} 2>/dev/null || ls -la {}' \
        --preview-window=right:50% \
        --header='ENTER=cd, CTRL-O=open in editor, CTRL-Y=copy path' \
        --bind='enter:accept,ctrl-o:execute($EDITOR {}),ctrl-y:execute-silent(echo {} | wl-copy)')

  if [[ -n "$dir" ]]; then
    cd "$dir" && eza --icons -la
  fi
}

# FZF Process Management
fzf_process() {
  local pid
  pid=$(ps -eo pid,ppid,user,comm --no-headers | \
    fzf --height 80% --reverse --border \
        --prompt="🔍 Process: " \
        --preview='ps -p {1} -o pid,ppid,user,comm,cmd --no-headers' \
        --preview-window=right:60% \
        --header='ENTER=kill, CTRL-K=kill -9' \
        --bind='enter:execute(kill {1}),ctrl-k:execute(kill -9 {1})')

  if [[ -n "$pid" ]]; then
    echo "Process $pid selected"
  fi
}

# FZF Git Branch Management
fzf_git_branch() {
  if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "Not in a git repository"
    return 1
  fi

  local branch
  branch=$(git branch -a --format='%(refname:short)' | \
    fzf --height 80% --reverse --border \
        --prompt="🌿 Git Branch: " \
        --preview='git log --oneline --graph --color=always {} | head -20' \
        --preview-window=right:60% \
        --header='ENTER=checkout, CTRL-D=delete, CTRL-M=merge' \
        --bind='enter:execute(git checkout {}),ctrl-d:execute(git branch -d {}),ctrl-m:execute(git merge {})')

  if [[ -n "$branch" ]]; then
    git checkout "$branch"
  fi
}

# FZF Git Commit Browser
fzf_git_log() {
  if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "Not in a git repository"
    return 1
  fi

  local commit
  commit=$(git log --oneline --graph --color=always | \
    fzf --height 80% --reverse --border \
        --prompt="📝 Git Log: " \
        --preview='git show --color=always {1}' \
        --preview-window=right:60% \
        --header='ENTER=show, CTRL-Y=copy hash' \
        --bind='ctrl-y:execute-silent(echo {1} | wl-copy)' \
        --ansi)

  if [[ -n "$commit" ]]; then
    local hash=$(echo "$commit" | awk '{print $1}' | sed 's/[^a-f0-9]//g')
    git show "$hash"
  fi
}

# FZF Zellij Session Management
fzf_zellij() {
  if ! command -v zellij >/dev/null 2>&1; then
    echo "Zellij not installed"
    return 1
  fi

  local sessions=($(zellij list-sessions 2>/dev/null | grep -v "No active sessions" || echo ""))
  local action

  if [[ ${#sessions[@]} -eq 0 ]]; then
    echo "No active sessions. Creating new session..."
    zellij
    return
  fi

  action=$(printf "attach:%s\nnew:Create New Session\nkill-all:Kill All Sessions" "${sessions[@]}" | \
    fzf --height 60% --reverse --border \
        --prompt="🖥️  Zellij: " \
        --preview='echo "Action: {1}"' \
        --delimiter=':' \
        --with-nth=2.. \
        --header='Select session or action')

  if [[ -n "$action" ]]; then
    local cmd=$(echo "$action" | cut -d':' -f1)
    local target=$(echo "$action" | cut -d':' -f2-)

    case "$cmd" in
      "attach") zellij attach "$target" ;;
      "new") zellij ;;
      "kill-all") zellij kill-all-sessions ;;
    esac
  fi
}

# === QUICK COPY FUNCTIONS ===

# Copy last command output
copy_last_output() {
  fc -ln -1 | wl-copy
  echo "Last command copied to clipboard"
}

# Copy current directory path
copy_pwd() {
  pwd | wl-copy
  echo "Current directory copied to clipboard"
}

# Copy file content to clipboard
copy_file() {
  if [[ -f "$1" ]]; then
    cat "$1" | wl-copy
    echo "File content copied to clipboard"
  else
    echo "File not found: $1"
  fi
}

# === VIM-STYLE TEXT SELECTION HELPERS ===

# Quick search in terminal output
terminal_search() {
  local query="$1"
  if [[ -z "$query" ]]; then
    echo "Usage: terminal_search <pattern>"
    return 1
  fi

  # Use zellij search if in zellij session
  if [[ -n "$ZELLIJ" ]]; then
    echo "Use Alt+/ to search in Zellij"
  else
    echo "Search pattern: $query"
    # Could integrate with terminal scrollback search here
  fi
}

# === GOOGLE INTEGRATION ===

# Google search from terminal
function google() {
  local query="$*"
  if [[ -z "$query" ]]; then
    echo "Usage: google <search terms>"
    return 1
  fi

  local url="https://www.google.com/search?q=$(echo "$query" | sed 's/ /+/g')"

  if command -v xdg-open >/dev/null 2>&1; then
    xdg-open "$url"
  elif command -v firefox >/dev/null 2>&1; then
    firefox "$url" &
  else
    echo "Search URL: $url"
  fi
}

# Google Cloud CLI shortcuts (if installed)
if command -v gcloud >/dev/null 2>&1; then
  alias gcp='gcloud'
  alias gcplist='gcloud projects list'
  alias gcpset='gcloud config set project'

  # FZF Google Cloud Project Selector
  fzf_gcp_project() {
    local project
    project=$(gcloud projects list --format="value(projectId)" 2>/dev/null | \
      fzf --height 60% --reverse --border \
          --prompt="☁️  GCP Project: " \
          --preview='gcloud projects describe {} 2>/dev/null' \
          --preview-window=right:50%)

    if [[ -n "$project" ]]; then
      gcloud config set project "$project"
      echo "Switched to project: $project"
    fi
  }
fi

# === KEY BINDINGS ===

# Bind enhanced functions to keys
zle -N fzf_file
zle -N fzf_dir
zle -N fzf_process
zle -N fzf_git_branch
zle -N fzf_git_log
zle -N fzf_zellij
zle -N copy_last_output
zle -N copy_pwd

# Key bindings (avoiding conflicts)
bindkey '^F' fzf_file              # Ctrl+F - Find file
bindkey '^D' fzf_dir               # Ctrl+D - Directory navigation
bindkey '^P' fzf_process           # Ctrl+P - Process management
bindkey '^G^B' fzf_git_branch      # Ctrl+G Ctrl+B - Git branches
bindkey '^G^L' fzf_git_log         # Ctrl+G Ctrl+L - Git log
bindkey '^Z' fzf_zellij            # Ctrl+Z - Zellij management
bindkey '^Y' copy_last_output      # Ctrl+Y - Copy last output
bindkey '^X^P' copy_pwd            # Ctrl+X Ctrl+P - Copy pwd

# === ALIASES FOR QUICK ACCESS ===
alias ff='fzf_file'
alias fd='fzf_dir'
alias fp='fzf_process'
alias gb='fzf_git_branch'
alias gl='fzf_git_log'
alias zj='fzf_zellij'
alias cpy='copy_last_output'
alias cpwd='copy_pwd'
alias search='terminal_search'

# === PERFORMANCE OPTIMIZATIONS ===
# Functions are defined in this file, no need for autoload

# Cache frequently used commands
typeset -A _workflow_cache
_workflow_cache[git_status]=""
_workflow_cache[zellij_sessions]=""

# Update cache periodically
_update_workflow_cache() {
  if git rev-parse --git-dir >/dev/null 2>&1; then
    _workflow_cache[git_status]=$(git status --porcelain 2>/dev/null)
  fi

  if command -v zellij >/dev/null 2>&1; then
    _workflow_cache[zellij_sessions]=$(zellij list-sessions 2>/dev/null)
  fi
}

# Update cache on directory change
chpwd_functions+=(_update_workflow_cache)
