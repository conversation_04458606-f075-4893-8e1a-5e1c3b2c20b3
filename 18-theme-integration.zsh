#!/usr/bin/env zsh
# Unified Theme System for Zsh/Zellij/Hyprland/Foot

# === THEME DEFINITIONS ===
declare -A THEMES
# Classic themes
THEMES[gruvbox-dark]="dark"
THEMES[gruvbox-light]="light"
THEMES[solarized-dark]="dark"
THEMES[solarized-light]="light"
THEMES[monokai]="dark"

# Catppuccin family
THEMES[catppuccin-mocha]="dark"
THEMES[catppuccin-macchiato]="dark"
THEMES[catppuccin-frappe]="dark"
THEMES[catppuccin-latte]="light"

# Tokyo Night family
THEMES[tokyo-night]="dark"
THEMES[tokyo-night-light]="light"
THEMES[tokyo-night-storm]="dark"

# Dracula family
THEMES[dracula]="dark"
THEMES[dracula-soft]="dark"

# Nord family
THEMES[nord]="dark"
THEMES[nord-light]="light"

# One Dark family
THEMES[one-dark]="dark"
THEMES[one-light]="light"

# Material family
THEMES[material-dark]="dark"
THEMES[material-light]="light"
THEMES[material-ocean]="dark"

# GitHub themes
THEMES[github-dark]="dark"
THEMES[github-light]="light"

# Ayu family
THEMES[ayu-dark]="dark"
THEMES[ayu-light]="light"
THEMES[ayu-mirage]="dark"

# Everforest family
THEMES[everforest-dark]="dark"
THEMES[everforest-light]="light"

# Rose Pine family
THEMES[rose-pine]="dark"
THEMES[rose-pine-moon]="dark"
THEMES[rose-pine-dawn]="light"

# Custom high-contrast themes
THEMES[prismatic-dark]="dark"
THEMES[prismatic-light]="light"
THEMES[steel-dark]="dark"
THEMES[obsidian]="dark"
THEMES[platinum]="light"
THEMES[copper]="dark"

# Additional popular themes
THEMES[kanagawa]="dark"
THEMES[nightfox]="dark"
THEMES[carbonfox]="dark"
THEMES[tokyonight-moon]="dark"

# New popular themes
THEMES[chocolate-gruvbox]="dark"
THEMES[monokai-pro]="dark"
THEMES[palenight]="dark"
THEMES[synthwave]="dark"
THEMES[horizon]="dark"
THEMES[shades-of-purple]="dark"
THEMES[cobalt2]="dark"
THEMES[night-owl]="dark"
THEMES[winter-is-coming]="dark"
THEMES[andromeda]="dark"
THEMES[atom-one-dark-pro]="dark"
THEMES[darcula]="dark"

# === CURRENT THEME TRACKING ===
CURRENT_THEME_FILE="$HOME/.config/current-theme"

get_current_theme() {
  if [[ -f "$CURRENT_THEME_FILE" ]]; then
    cat "$CURRENT_THEME_FILE"
  else
    echo "gruvbox-dark"
  fi
}

set_current_theme() {
  echo "$1" > "$CURRENT_THEME_FILE"
}

# === FOOT TERMINAL THEME ===
apply_foot_theme() {
  local theme="$1"
  if [[ -x "$HOME/.config/foot/theme-switcher.sh" ]]; then
    "$HOME/.config/foot/theme-switcher.sh" "$theme"
  fi
}

# === ZELLIJ THEME ===
apply_zellij_theme() {
  local theme="$1"
  local zellij_config="$HOME/.config/zellij/config.kdl"

  # Map our themes to zellij themes
  case "$theme" in
    "gruvbox-dark"|"gruvbox-mono"|"gruvbox-high-contrast")
      sed -i 's/theme .*/theme "gruvbox-dark"/' "$zellij_config" 2>/dev/null
      ;;
    "gruvbox-light")
      sed -i 's/theme .*/theme "gruvbox-light"/' "$zellij_config" 2>/dev/null
      ;;
  esac
}

# === P10K THEME ===
apply_p10k_theme() {
  local theme="$1"
  local p10k_config="$HOME/.config/zsh/.p10k.zsh"

  if [[ ! -f "$p10k_config" ]]; then
    return 1
  fi

  case "$theme" in
    "gruvbox-light")
      # Light theme adjustments
      sed -i 's/typeset -g POWERLEVEL9K_BACKGROUND=.*/typeset -g POWERLEVEL9K_BACKGROUND=7/' "$p10k_config"
      sed -i 's/typeset -g POWERLEVEL9K_FOREGROUND=.*/typeset -g POWERLEVEL9K_FOREGROUND=0/' "$p10k_config"
      ;;
    *)
      # Dark theme (default)
      sed -i 's/typeset -g POWERLEVEL9K_BACKGROUND=.*/typeset -g POWERLEVEL9K_BACKGROUND=0/' "$p10k_config"
      sed -i 's/typeset -g POWERLEVEL9K_FOREGROUND=.*/typeset -g POWERLEVEL9K_FOREGROUND=7/' "$p10k_config"
      ;;
  esac
}

# === UNIFIED THEME SWITCHER ===
switch_theme() {
  local theme="$1"
  local theme_fast="$HOME/.config/zsh/theme_system/theme-fast"

  if [[ -z "$theme" ]]; then
    # Get available themes from theme-fast
    if [[ -x "$theme_fast" ]]; then
      theme=$("$theme_fast" list | \
        fzf --height 60% --reverse --border \
            --prompt="🎨 Select Theme: " \
            --preview='echo "Theme: {}" && echo "Type: dark/light"' \
            --header='Select a theme to apply across all applications')
    else
      echo "❌ theme-fast not found"
      return 1
    fi
  fi

  if [[ -z "$theme" ]]; then
    echo "❌ No theme selected"
    return 1
  fi

  echo "🎨 Applying theme: $theme"

  # Use theme-fast for comprehensive theme application
  if [[ -x "$HOME/.config/zsh/theme_system/theme-fast" ]]; then
    "$HOME/.config/zsh/theme_system/theme-fast" "$theme"
  else
    # Fallback to old method
    apply_foot_theme "$theme"
    apply_zellij_theme "$theme"
    apply_p10k_theme "$theme"
    set_current_theme "$theme"
    echo "✅ Theme applied: $theme"
    echo "💡 Restart terminal or reload zsh to see all changes"
  fi
}

# === THEME STATUS ===
theme_status() {
  local theme_fast="$HOME/.config/zsh/theme_system/theme-fast"

  if [[ -x "$theme_fast" ]]; then
    "$theme_fast" list
  else
    echo "❌ theme-fast not found"
  fi
}

# === AUTO-APPLY THEME ON STARTUP ===
auto_apply_theme() {
  local current=$(get_current_theme)
  if [[ -n "$current" ]] && [[ -n "${THEMES[$current]}" ]]; then
    # Silently apply current theme
    apply_zellij_theme "$current" 2>/dev/null
    apply_p10k_theme "$current" 2>/dev/null
  fi
}

# === THEME COMMAND ===
# Remove any existing alias first
unalias theme 2>/dev/null

# One simple, fast theme command
theme() {
    local theme_name="$1"
    if [[ -z "$theme_name" ]]; then
        # No argument - show FZF selector
        "$HOME/.config/zsh/theme_system/theme-switcher-enhanced"
        return
    fi

    # Direct theme application - ultra fast
    cd "$HOME/.config/zsh/theme_system"
    ./theme-fast "$theme_name"
    cd - > /dev/null
}

# === ZELLIJ ALIASES ===
alias zj='zellij'
alias zjn='zellij --session main'
alias zja='zellij attach'
alias zjl='zellij list-sessions'
alias zjk='zellij kill-session'

# === ZELLIJ TEXT SELECTION HELP ===
# Since Zellij doesn't have tmux-style visual selection, here are the best methods:
#
# METHOD 1: Mouse Selection (Easiest)
# - Hold Shift + drag with mouse to select text
# - Right-click to copy, or Ctrl+Shift+C
#
# METHOD 2: Search-based Selection
# - Alt+V or Ctrl+V to enter scroll mode
# - Use / to search for text you want
# - Press y to copy the search result
#
# METHOD 3: Terminal Selection (Most Reliable)
# - Use your terminal's native selection (Shift+drag)
# - This works outside of Zellij's control
#
# METHOD 4: External Tools
# - Use 'history | grep' to find commands
# - Use 'journalctl' output redirection: journalctl > /tmp/log.txt
#
function zellij-help() {
    echo "🔧 Zellij Text Selection Methods:"
    echo ""
    echo "1. 🖱️  Mouse Selection (Easiest):"
    echo "   • Hold Shift + drag to select"
    echo "   • Right-click or Ctrl+Shift+C to copy"
    echo ""
    echo "2. 🔍 Search-based Selection:"
    echo "   • Alt+V to enter scroll mode"
    echo "   • / to search for text"
    echo "   • y to copy search result"
    echo ""
    echo "3. 📋 Terminal Selection:"
    echo "   • Use terminal's native Shift+drag"
    echo "   • Works outside Zellij control"
    echo ""
    echo "4. 🛠️  External Tools:"
    echo "   • history | grep 'command'"
    echo "   • journalctl > /tmp/log.txt"
    echo ""
    echo "💡 For long outputs, consider piping to files or using external tools"
}

alias zh='zellij-help'

# === THEME SWITCHER HOTKEY ===
# The theme switcher is available as a pyprland scratchpad
# Hotkey: Super+Shift+M (configured in hyprland binds.conf)

# === FZF THEME INTEGRATION ===
# Source FZF theme colors if available
[[ -f ~/.config/zsh/fzf-theme.zsh ]] && source ~/.config/zsh/fzf-theme.zsh

# === INTEGRATION WITH HOTKEY MENU ===
# Add theme switching to the hotkey menu
if typeset -f hotkey_menu >/dev/null 2>&1; then
  # Theme switching is already integrated via the theme command
  :
fi

# Apply theme on startup (silent)
auto_apply_theme
