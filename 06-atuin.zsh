# Atuin: advanced shell history (requires yay -S atuin)
# Style: full (rich info, icons, context)
export ATUIN_STYLE=full
export ATUIN_SEARCH_MODE=fuzzy
export ATUIN_SEARCH_SCORING=recent
export ATUIN_NOBIND=false
export ATUIN_LOG_LEVEL=info
# Atuin init must be before plugin loading for keybinds to work
if command -v atuin > /dev/null 2>&1; then
  eval "$(atuin init zsh)"
  # Bind Ctrl+R to Atuin search in both vi insert and normal mode
  bindkey -M viins '^R' _atuin_search_widget
  bindkey -M vicmd '^R' _atuin_search_widget
fi
# Note: Atuin not found message removed to prevent console output during init
