# Optimized FZF settings with performance enhancements and usability
export FZF_DEFAULT_OPTS="--height 80% --reverse --preview 'bat --style=numbers --color=always {} 2>/dev/null || exa --icons -l {} 2>/dev/null || ls -la {}' --bind 'ctrl-y:execute-silent(echo {} | pbcopy),ctrl-space:toggle-preview,ctrl-d:half-page-down,ctrl-u:half-page-up,ctrl-/:change-preview-window(down|hidden|)' --color=fg:#ebdbb2,bg:#282828,hl:#fabd2f,fg+:#ebdbb2,bg+:#3c3836,hl+:#fabd2f,info:#83a598,prompt:#bdae93,spinner:#fabd2f,pointer:#83a598,marker:#fe8019,header:#665c54"
export FZF_DEFAULT_COMMAND='fd --type f --hidden --exclude .git --exclude node_modules --exclude .cache --exclude .venv --exclude venv --follow'
export FZF_CTRL_T_COMMAND="$FZF_DEFAULT_COMMAND"
export FZF_ALT_C_COMMAND="fd --type d --hidden --exclude .git --exclude node_modules --exclude .cache --follow"
export FZF_CTRL_T_OPTS="--preview 'bat --style=numbers --color=always {} 2>/dev/null || ls -la {}'"
export FZF_ALT_C_OPTS="--preview 'ls -la {}'"
