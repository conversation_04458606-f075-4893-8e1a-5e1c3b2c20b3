#!/usr/bin/env zsh
# FZF Hotkey Menu - Access all keybinds and features

# === HOTKEY MENU FUNCTION ===
hotkey_menu() {
  local hotkeys=(
    # Zellij Navigation
    "Alt+h/j/k/l:Navigate panes (vim-style):<PERSON><PERSON><PERSON>"
    "Alt+t:New tab:<PERSON>ellij"
    "Alt+w:Close tab:Zellij"
    "Alt+n:New pane:Zellij"
    "Alt+p:New pane (alternative):Zellij"
    "Alt+x:Close pane:<PERSON>ellij"
    "Alt+v:Vertical split:Zellij"
    "Alt+-:Horizontal split:Zellij"
    "Alt+f:Toggle fullscreen:Zellij"
    "Alt+s:Scroll mode:Zellij"
    "Alt+y:Copy selection:Zellij"
    "Alt+/:Search mode:Zellij"
    "Alt+1-9:Switch to tab 1-9:Zellij"
    "Alt+g:New tab (bottom bar):Zellij"
    "Ctrl+s:Scroll mode (bottom bar):Zellij"
    "Alt+q:Toggle fullscreen (bottom bar):<PERSON><PERSON>j"

    # Zsh Workflow
    "Ctrl+A:Alias finder:Zsh"
    "Ctrl+R:History search (Atuin):Zsh"
    "Ctrl+F:File finder (FZF):Zsh"
    "Ctrl+D:Directory navigation:Zsh"
    "Ctrl+P:Process manager:Zsh"
    "Ctrl+G:Smart directory navigation:Zsh"
    "Ctrl+Y:Copy last command output:Zsh"
    "Backspace:Delete single character:Zsh"
    "Ctrl+Backspace:Precise word delete:Zsh"
    "Shift+Backspace:Fast word delete:Zsh"
    "Ctrl+W:Fast word delete:Zsh"

    # Git Shortcuts
    "gs:Git status:Git"
    "ga:Git add:Git"
    "gc:Git commit:Git"
    "gp:Git push:Git"
    "gl:Git pull:Git"
    "gb:Git branch selector (FZF):Git"
    "glog:Git log browser:Git"

    # Project Management
    "proj:Project switcher:Development"
    "cdf:Directory navigation with preview:Development"
    "ff:File finder with preview:Development"
    "fp:Process manager:Development"

    # System Management
    "sysup:Update system:System"
    "sysclean:Clean system:System"
    "sysinfo:System information:System"
    "reload:Reload zsh config:System"

    # Zellij Session Management
    "zjs:Smart Zellij attach/create:Zellij"
    "zj:Zellij session manager (FZF):Zellij"
    "zja:Attach to session:Zellij"
    "zjn:New session:Zellij"
    "zjl:List sessions:Zellij"

    # Manual Text Selection (in Zellij scroll mode)
    "Alt+s then v:Visual character selection:Zellij"
    "Alt+s then V:Visual line selection:Zellij"
    "Alt+s then Ctrl+v:Visual block selection:Zellij"
    "Alt+s then y:Copy selection to clipboard:Zellij"
    "Alt+s then d:Delete selection:Zellij"
    "Alt+s then w/b:Word forward/backward:Zellij"
    "Alt+s then 0/$:Line start/end:Zellij"
    "Alt+s then g/G:Buffer start/end:Zellij"
    "Alt+s then hjkl:Extend selection:Zellij"

    # Navigation Enhancements
    "Alt+h:Back in directory history:Navigation"
    "Alt+l:Forward in directory history:Navigation"
    "Alt+k:Up directory:Navigation"
    "Alt+j:Down into first directory:Navigation"
    "bookmark <name>:Bookmark current directory:Navigation"
    "jump:Jump to bookmark (FZF):Navigation"
    "...:Go up 2 directories:Navigation"
    "....:Go up 3 directories:Navigation"

    # Media Applications (Hyprland)
    "Super+Shift+Return:Floating terminal:Hyprland"
    "Super+Shift+Z:Zsh terminal:Hyprland"
    "Super+Shift+J:Zellij session:Hyprland"
    "Super+Shift+A:Ani-cli:Hyprland"
    "Super+Shift+M:System monitor:Hyprland"
    "Ctrl+M:Spotify player:Hyprland"
    "Super+Shift+C:Cava visualizer:Hyprland"

    # Development Tools
    "google <terms>:Google search:Tools"
    "weather:Weather info:Tools"
    "myip:Show public IP:Tools"
    "ports:Show listening ports:Tools"
    "cpu:CPU monitor:Tools"
    "mem:Memory monitor:Tools"
  )

  local selection=$(printf '%s\n' "${hotkeys[@]}" | \
    fzf --height 80% --reverse --border \
        --prompt="⌨️  Hotkeys & Features: " \
        --preview='echo "Key: {1}" && echo "Action: {2}" && echo "Category: {3}" && echo "" && echo "Press ENTER to copy to clipboard or ESC to cancel"' \
        --preview-window=right:50% \
        --delimiter=':' \
        --with-nth=1,2 \
        --header='🎯 All Available Hotkeys and Features - ENTER to copy, ESC to cancel' \
        --bind='enter:accept,esc:cancel')

  if [[ -n "$selection" ]]; then
    local key=$(echo "$selection" | cut -d':' -f1)
    local action=$(echo "$selection" | cut -d':' -f2)
    local category=$(echo "$selection" | cut -d':' -f3)

    # Copy to clipboard
    echo "$key" | wl-copy 2>/dev/null || echo "$key" | xclip -selection clipboard 2>/dev/null

    echo "📋 Copied to clipboard: $key"
    echo "🎯 Action: $action"
    echo "📁 Category: $category"

    # If it's a command (not a key combination), ask if user wants to execute
    if [[ "$key" =~ ^[a-zA-Z] ]] && [[ ! "$key" =~ \+ ]]; then
      echo ""
      read "execute?Execute this command now? (y/N): "
      if [[ "$execute" =~ ^[Yy]$ ]]; then
        eval "$key"
      fi
    fi
  fi
}

# === QUICK HELP FUNCTION ===
quick_help() {
  echo "🚀 QUICK REFERENCE"
  echo "=================="
  echo ""
  echo "🎯 Essential Shortcuts:"
  echo "  Ctrl+A          - This hotkey menu"
  echo "  Ctrl+R          - History search"
  echo "  Alt+h/j/k/l     - Navigate Zellij panes"
  echo "  Alt+t/w         - New/close Zellij tab"
  echo "  Alt+n/x         - New/close Zellij pane"
  echo ""
  echo "🔍 FZF Features:"
  echo "  Ctrl+F          - Find files"
  echo "  Ctrl+D          - Navigate directories"
  echo "  proj            - Switch projects"
  echo "  gb              - Git branch selector"
  echo ""
  echo "⚡ Quick Commands:"
  echo "  gs/ga/gc/gp     - Git workflow"
  echo "  sysup           - Update system"
  echo "  reload          - Reload zsh"
  echo "  zjs             - Smart Zellij"
  echo ""
  echo "📚 For complete list: Use Ctrl+A or run 'hotkey_menu'"
}

# === ZLE WIDGET SETUP ===
zle -N hotkey_menu
zle -N quick_help

# === KEY BINDINGS ===
bindkey '^A' hotkey_menu              # Ctrl+A - Full hotkey menu
bindkey '^[?' quick_help              # Alt+? - Quick help

# === ALIASES ===
alias hk='hotkey_menu'
alias help='quick_help'
alias keys='hotkey_menu'
alias shortcuts='hotkey_menu'

# === INTEGRATION WITH EXISTING ALIAS FINDER ===
# Replace the old alias_finder_fzf with our comprehensive hotkey menu
alias_finder_fzf() {
  hotkey_menu
}

# === STARTUP MESSAGE (disabled to prevent console output during init) ===
# Tip: Press Ctrl+A for hotkey menu, or 'help' for quick reference
