#!/usr/bin/env zsh
# Optimized Aliases & Shortcuts for Seamless Terminal Experience
# Fast, organized, and FZF-integrated alias management

# === PERFORMANCE ALIASES ===
alias reload='exec zsh'
alias zshconfig='$EDITOR ~/.zshrc'
alias zshdir='cd ~/.config/zsh'

# === SYSTEM MANAGEMENT ===
alias sysup='sudo pacman -Syu && yay -Syu'
alias sysclean='sudo pacman -Rns $(pacman -Qtdq) 2>/dev/null; sudo pacman -Sc'
alias sysinfo='fastfetch'
alias diskspace='df -h | grep -E "^/dev"'
alias meminfo='free -h'

# === ZELLIJ SESSION MANAGEMENT ===
alias zj='zellij'
alias zja='zellij attach'
alias zjn='zellij -s'
alias zjl='zellij list-sessions'
alias zjk='zellij kill-session'
alias zjka='zellij kill-all-sessions'

# === GIT SHORTCUTS ===
alias gs='git status'
alias ga='git add'
alias gc='git commit -m'
alias gp='git push'
alias gl='git pull'
alias gd='git diff'
alias gco='git checkout'
alias gb='git branch'
alias glog='git log --oneline --graph --decorate'

# === NAVIGATION ===
alias ..='cd ..'
alias ...='cd ../..'
alias ....='cd ../../..'
alias ~='cd ~'
alias -- -='cd -'

# === FILE OPERATIONS ===
alias cp='cp -i'
alias mv='mv -i'
alias rm='rm -i'
alias mkdir='mkdir -p'

# === DEVELOPMENT ===
alias py='python3'
alias pip='pip3'
alias venv='python3 -m venv'
alias activate='source venv/bin/activate'

# === FZF-POWERED ALIAS FINDER ===
# Fast alias discovery and execution
alias_finder_fzf() {
  local aliases=(
    "zj:zellij:Start Zellij terminal multiplexer"
    "zja:zellij attach:Attach to existing Zellij session"
    "zjn:zellij -s:Create new named Zellij session"
    "zjl:zellij list-sessions:List all Zellij sessions"
    "sysup:sudo pacman -Syu && yay -Syu:Update system packages"
    "sysclean:cleanup:Clean system packages and cache"
    "gs:git status:Show git repository status"
    "ga:git add:Stage files for commit"
    "gc:git commit -m:Commit staged changes"
    "gp:git push:Push commits to remote"
    "gl:git pull:Pull changes from remote"
    "gd:git diff:Show file differences"
    "reload:exec zsh:Reload zsh configuration"
    "zshconfig:edit ~/.zshrc:Edit zsh configuration"
    "py:python3:Run Python 3"
    "venv:python3 -m venv:Create Python virtual environment"
    "activate:source venv/bin/activate:Activate Python virtual environment"
    "sysinfo:fastfetch:Show system information"
    "diskspace:df -h:Show disk usage"
    "meminfo:free -h:Show memory usage"
  )
  
  local selection=$(printf '%s\n' "${aliases[@]}" | \
    fzf --height 60% --reverse --border \
        --prompt="🔍 Find Alias: " \
        --preview='echo {3..}' \
        --preview-window=right:50% \
        --bind='enter:accept,esc:cancel' \
        --header='Press ENTER to execute, ESC to cancel')
  
  if [[ -n "$selection" ]]; then
    local cmd=$(echo "$selection" | cut -d':' -f2)
    echo "Executing: $cmd"
    eval "$cmd"
  fi
}

# Bind to Ctrl+A for quick access
zle -N alias_finder_fzf
bindkey '^A' alias_finder_fzf

# === SMART CD WITH FZF ===
# Enhanced directory navigation
cdf() {
  local dir
  dir=$(fd --type d --hidden --exclude .git --exclude node_modules --exclude .cache 2>/dev/null | \
    fzf --height 60% --reverse --border \
        --prompt="📁 Navigate to: " \
        --preview='ls -la {}' \
        --preview-window=right:50%)
  
  if [[ -n "$dir" ]]; then
    cd "$dir" && ls
  fi
}

# === PROJECT SWITCHER ===
# Quick project navigation
proj() {
  local project_dirs=(
    "$HOME/projects"
    "$HOME/dev"
    "$HOME/code"
    "$HOME/work"
    "$HOME/.config"
  )
  
  local projects=()
  for dir in "${project_dirs[@]}"; do
    if [[ -d "$dir" ]]; then
      projects+=($(find "$dir" -maxdepth 2 -type d -name ".git" -exec dirname {} \; 2>/dev/null))
    fi
  done
  
  if [[ ${#projects[@]} -eq 0 ]]; then
    echo "No projects found in common directories"
    return 1
  fi
  
  local selection=$(printf '%s\n' "${projects[@]}" | \
    fzf --height 60% --reverse --border \
        --prompt="🚀 Open Project: " \
        --preview='ls -la {} && echo && git -C {} status 2>/dev/null || echo "Not a git repository"' \
        --preview-window=right:60%)
  
  if [[ -n "$selection" ]]; then
    cd "$selection"
    echo "📂 Switched to: $(basename "$selection")"
    ls -la
  fi
}

# === PROCESS MANAGEMENT ===
alias psg='ps aux | grep -v grep | grep'
alias ports='netstat -tuln'
alias listening='lsof -i -P -n | grep LISTEN'

# === QUICK EDITS ===
alias hosts='sudo $EDITOR /etc/hosts'
alias fstab='sudo $EDITOR /etc/fstab'

# === PERFORMANCE MONITORING ===
alias cpu='top -o %CPU'
alias mem='top -o %MEM'
alias iotop='sudo iotop'

# === NETWORK ===
alias myip='curl -s ifconfig.me'
alias localip='ip route get 1 | awk "{print \$7}"'
alias ping='ping -c 5'
alias wget='wget -c'

# === ARCHIVE OPERATIONS ===
alias tarx='tar -xvf'
alias tarc='tar -cvf'
alias tarz='tar -czvf'
alias untar='tar -xvf'

# === MISC UTILITIES ===
alias weather='curl wttr.in'
alias cheat='curl cheat.sh'
alias qr='qrencode -t ansiutf8'
alias timer='echo "Timer started. Press Ctrl+C to stop." && date && read && date'

# === ZELLIJ INTEGRATION ===
# Auto-attach to zellij session or create new one
zellij_smart() {
  if [[ -z "$ZELLIJ" ]]; then
    if zellij list-sessions 2>/dev/null | grep -q .; then
      zellij attach
    else
      zellij
    fi
  else
    echo "Already in Zellij session"
  fi
}
alias zjs='zellij_smart'

# === HELP SYSTEM ===
alias_help() {
  echo "🚀 Optimized Zsh Aliases & Shortcuts"
  echo "=================================="
  echo "📋 Alias Management:"
  echo "  Ctrl+A          - Interactive alias finder (FZF)"
  echo "  alias_help      - Show this help"
  echo ""
  echo "🗂️  Navigation:"
  echo "  cdf             - FZF directory navigation"
  echo "  proj            - Project switcher"
  echo "  ..              - Go up one directory"
  echo ""
  echo "🔧 Zellij:"
  echo "  zjs             - Smart Zellij attach/create"
  echo "  zj/zja/zjn/zjl  - Zellij shortcuts"
  echo ""
  echo "⚡ System:"
  echo "  sysup/sysclean  - System maintenance"
  echo "  reload          - Reload zsh config"
  echo ""
  echo "🔍 Git:"
  echo "  gs/ga/gc/gp/gl  - Git shortcuts"
  echo ""
  echo "For more aliases, use Ctrl+A or check ~/.config/zsh/14-aliases-optimized.zsh"
}

# Performance: Only load heavy functions when needed
autoload -Uz cdf proj alias_finder_fzf zellij_smart
