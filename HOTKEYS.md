# 🚀 Complete Hotkey & Workflow Guide

## 🎯 **Quick Reference Card**

### Essential Shortcuts
| Key | Action | Context |
|-----|--------|---------|
| `Ctrl+A` | Interactive alias finder | Zsh |
| `Ctrl+R` | Atuin history search | Zsh |
| `Ctrl+F` | FZF file finder | Zsh |
| `Ctrl+D` | FZF directory navigation | Zsh |
| `Alt+h/j/k/l` | Navigate panes (vim-style) | Zellij |
| `Alt+t` | New tab | Zellij |
| `Alt+n` | New pane | Zellij |
| `Alt+x` | Close pane | Zellij |

---

## 🖥️ **Zellij Hotkeys** (Optimized Layout)

### Tab Management
| Key | Action |
|-----|--------|
| `Alt+t` | New tab |
| `Alt+w` | Close tab |
| `Alt+1-9` | Switch to tab 1-9 |
| `Alt+Tab` | Next tab |
| `Alt+Shift+Tab` | Previous tab |

### Pane Management
| Key | Action |
|-----|--------|
| `Alt+n` | New pane |
| `Alt+x` | Close pane |
| `Alt+\|` | Vertical split |
| `Alt+-` | Horizontal split |
| `Alt+f` | Toggle fullscreen |

### Navigation (Vim-Style)
| Key | Action |
|-----|--------|
| `Alt+h` | Move focus left |
| `Alt+j` | Move focus down |
| `Alt+k` | Move focus up |
| `Alt+l` | Move focus right |

### Pane Moving
| Key | Action |
|-----|--------|
| `Alt+H` | Move pane left |
| `Alt+J` | Move pane down |
| `Alt+K` | Move pane up |
| `Alt+L` | Move pane right |

### Resizing
| Key | Action |
|-----|--------|
| `Alt+=` | Increase pane size |
| `Alt+_` | Decrease pane size |
| `Ctrl+h/j/k/l` | Resize in direction |

### Text Selection & Copy
| Key | Action |
|-----|--------|
| `Alt+s` | Enter scroll mode |
| `Alt+y` | Copy selection |
| `Alt+/` | Search mode |
| `Alt+e` | Edit scrollback |

### Scroll Mode (Vim-Style)
| Key | Action |
|-----|--------|
| `j/k` | Scroll down/up |
| `h/l` | Half page scroll |
| `g/G` | Top/bottom |
| `q/Esc` | Exit scroll mode |

---

## 🐚 **Zsh Workflow Hotkeys**

### FZF Integration
| Key | Action |
|-----|--------|
| `Ctrl+F` | Find and open file |
| `Ctrl+D` | Navigate directories |
| `Ctrl+P` | Process management |
| `Ctrl+G Ctrl+B` | Git branch selector |
| `Ctrl+G Ctrl+L` | Git log browser |
| `Ctrl+Z` | Zellij session manager |

### Quick Copy
| Key | Action |
|-----|--------|
| `Ctrl+Y` | Copy last command output |
| `Ctrl+X Ctrl+P` | Copy current directory |

### Alias Management
| Key | Action |
|-----|--------|
| `Ctrl+A` | Interactive alias finder |

### History & Search
| Key | Action |
|-----|--------|
| `Ctrl+R` | Atuin fuzzy history search |
| `Ctrl+T` | FZF file search |
| `Alt+C` | FZF directory search |

---

## 🎮 **Hyprland Integration**

### Terminal Launching
| Key | Action |
|-----|--------|
| `Super+Return` | Launch foot terminal (mini) |
| `Super+Shift+Return` | Launch foot terminal (floating) |
| `Super+Ctrl+Return` | Launch foot terminal (large) |
| `Super+Shift+Z` | Launch zsh in foot |

### Window Management
| Key | Action |
|-----|--------|
| `Super+h/j/k/l` | Focus window |
| `Super+Shift+h/j/k/l` | Move window |
| `Super+1-9` | Switch workspace |
| `Super+Shift+1-9` | Move to workspace |

---

## 🛠️ **Development Workflow**

### Git Shortcuts
| Command | Action |
|---------|--------|
| `gs` | Git status |
| `ga` | Git add |
| `gc` | Git commit -m |
| `gp` | Git push |
| `gl` | Git pull |
| `gb` | FZF git branch selector |
| `glog` | Git log --oneline --graph |

### Project Management
| Command | Action |
|---------|--------|
| `proj` | FZF project switcher |
| `cdf` | FZF directory navigation |
| `ff` | FZF file finder |
| `fp` | FZF process manager |

### System Management
| Command | Action |
|---------|--------|
| `sysup` | Update system packages |
| `sysclean` | Clean package cache |
| `sysinfo` | Show system info |
| `reload` | Reload zsh config |

### Zellij Management
| Command | Action |
|---------|--------|
| `zjs` | Smart Zellij attach/create |
| `zj` | FZF Zellij session manager |
| `zja` | Zellij attach |
| `zjn` | New Zellij session |
| `zjl` | List sessions |
| `zjk` | Kill session |

---

## 🔍 **FZF Features**

### File Operations
- **Preview**: Automatic file preview with `bat` or `cat`
- **Actions**: 
  - `Enter` - Open/select
  - `Ctrl+Y` - Copy path to clipboard
  - `Ctrl+O` - Open in editor
  - `Esc` - Cancel

### Git Integration
- **Branch Preview**: Shows recent commits
- **Log Preview**: Shows commit details
- **Actions**:
  - `Enter` - Checkout/show
  - `Ctrl+D` - Delete branch
  - `Ctrl+M` - Merge branch
  - `Ctrl+Y` - Copy hash

### Process Management
- **Preview**: Shows process details
- **Actions**:
  - `Enter` - Kill process
  - `Ctrl+K` - Force kill (kill -9)

---

## 🎯 **Vim Integration**

### Text Selection (in Zellij scroll mode)
| Key | Action |
|-----|--------|
| `v` | Visual mode |
| `V` | Line visual mode |
| `y` | Yank (copy) |
| `d` | Delete |
| `w/b` | Word forward/backward |
| `/` | Search forward |
| `?` | Search backward |
| `n/N` | Next/previous match |

### Navigation
| Key | Action |
|-----|--------|
| `h/j/k/l` | Move cursor |
| `w/b` | Word forward/backward |
| `0/$` | Line start/end |
| `gg/G` | File start/end |
| `Ctrl+f/b` | Page forward/backward |

---

## 🌐 **Google Integration**

### Commands
| Command | Action |
|---------|--------|
| `google <terms>` | Search Google in browser |
| `gcp` | Google Cloud CLI |
| `gcplist` | List GCP projects |
| `fzf_gcp_project` | FZF GCP project selector |

---

## 🚀 **Performance Tips**

### Startup Optimization
- Configuration loads in < 0.01s
- Lazy loading for heavy functions
- Smart caching for git status and sessions

### Memory Usage
- Functions loaded only when needed
- Cache frequently used data
- Minimal plugin footprint

### Workflow Efficiency
- Keyboard-driven navigation
- Context-aware previews
- Smart defaults and shortcuts

---

## 🔧 **Troubleshooting**

### Common Issues
| Issue | Solution |
|-------|----------|
| Slow startup | Run `time zsh -i -c exit` |
| FZF not working | Check `fzf --version` |
| Zellij errors | Use optimized config |
| Git shortcuts fail | Ensure in git repo |

### Performance Commands
| Command | Purpose |
|---------|---------|
| `p10k-clean` | Clean prompt cache |
| `p10k-rebuild` | Rebuild prompt cache |
| `alias_help` | Show all aliases |
| `reload` | Reload zsh config |

---

## 📚 **Learning Path**

### Beginner (Essential)
1. Learn `Ctrl+A` for alias discovery
2. Master `Alt+h/j/k/l` for Zellij navigation
3. Use `Ctrl+R` for history search
4. Practice `gs/ga/gc/gp` for git workflow

### Intermediate
1. Explore FZF functions (`Ctrl+F`, `Ctrl+D`)
2. Use project switcher (`proj`)
3. Master Zellij modes (`Alt+s`, `Alt+/`)
4. Learn text selection in scroll mode

### Advanced
1. Customize key bindings
2. Create custom FZF functions
3. Optimize workflow for specific tasks
4. Integrate with external tools

---

*This guide covers the complete optimized workflow. Use `alias_help` for quick reference or `Ctrl+A` for interactive discovery.*
