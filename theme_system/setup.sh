#!/bin/bash
# Universal Theme System Setup Script

set -e

THEME_DIR="$HOME/.config/zsh/theme_system"
BACKUP_DIR="$HOME/.config/theme_backups"

echo "🎨 Universal Theme System Setup"
echo "==============================="

# Check if we're in the right directory
if [[ ! -f "theme" ]] || [[ ! -d "src" ]]; then
    echo "❌ Error: Please run this script from the theme_system directory"
    exit 1
fi

# Create backup directory
echo "📁 Creating backup directory..."
mkdir -p "$BACKUP_DIR"

# Backup existing configs
echo "💾 Backing up existing configurations..."
backup_config() {
    local config_path="$1"
    local app_name="$2"
    
    if [[ -f "$config_path" ]]; then
        local backup_file="$BACKUP_DIR/${app_name}-$(date +%Y%m%d-%H%M%S).backup"
        cp "$config_path" "$backup_file"
        echo "  ✓ Backed up $config_path to $backup_file"
    fi
}

backup_config "$HOME/.config/foot/foot.ini" "foot"
backup_config "$HOME/.config/zellij/config.kdl" "zellij"
backup_config "$HOME/.config/hypr/hyprland.conf" "hyprland"
backup_config "$HOME/.config/gtk-3.0/gtk.css" "gtk3"
backup_config "$HOME/.config/gtk-4.0/gtk.css" "gtk4"

# Check Python version
echo "🐍 Checking Python version..."
if ! python3 -c "import sys; assert sys.version_info >= (3, 8)" 2>/dev/null; then
    echo "❌ Error: Python 3.8+ required"
    echo "   Current version: $(python3 --version 2>/dev/null || echo 'Not found')"
    exit 1
fi
echo "  ✓ Python version OK"

# Install Python dependencies
echo "📦 Installing Python dependencies..."
if command -v pipx >/dev/null 2>&1; then
    echo "  Using pipx (recommended)..."
    pipx install -r requirements.txt
elif command -v pip3 >/dev/null 2>&1; then
    echo "  Using pip3..."
    pip3 install --user -r requirements.txt
else
    echo "❌ Error: Neither pipx nor pip3 found"
    echo "   Please install pip: sudo pacman -S python-pip"
    exit 1
fi
echo "  ✓ Dependencies installed"

# Make CLI executable
echo "🔧 Making CLI executable..."
chmod +x theme
echo "  ✓ CLI is now executable"

# Check FZF
echo "🔍 Checking FZF..."
if command -v fzf >/dev/null 2>&1; then
    echo "  ✓ FZF found"
else
    echo "⚠️  Warning: FZF not found"
    echo "   Install with: sudo pacman -S fzf"
    echo "   Interactive theme selection will not work without FZF"
fi

# Create initial config
echo "⚙️  Creating initial configuration..."
if [[ ! -f "config.json" ]]; then
    ./theme status >/dev/null 2>&1 || true
    echo "  ✓ Configuration created"
else
    echo "  ✓ Configuration already exists"
fi

# Test theme system
echo "🧪 Testing theme system..."
if ./theme list >/dev/null 2>&1; then
    echo "  ✓ Theme system working"
else
    echo "❌ Error: Theme system test failed"
    echo "   Check the installation and try again"
    exit 1
fi

# Create directories
echo "📁 Creating required directories..."
mkdir -p "$HOME/.config/foot"
mkdir -p "$HOME/.config/zellij"
mkdir -p "$HOME/.config/hypr"
mkdir -p "$HOME/.config/gtk-3.0"
mkdir -p "$HOME/.config/gtk-4.0"
echo "  ✓ Directories created"

# Add to PATH (optional)
echo "🔗 Setting up PATH..."
SHELL_RC=""
if [[ "$SHELL" == *"zsh"* ]]; then
    SHELL_RC="$HOME/.zshrc"
elif [[ "$SHELL" == *"bash"* ]]; then
    SHELL_RC="$HOME/.bashrc"
fi

if [[ -n "$SHELL_RC" ]] && [[ -f "$SHELL_RC" ]]; then
    if ! grep -q "theme_system" "$SHELL_RC"; then
        echo "" >> "$SHELL_RC"
        echo "# Universal Theme System" >> "$SHELL_RC"
        echo "export PATH=\"\$HOME/.config/zsh/theme_system:\$PATH\"" >> "$SHELL_RC"
        echo "  ✓ Added to PATH in $SHELL_RC"
    else
        echo "  ✓ Already in PATH"
    fi
fi

# Integration with existing zsh config
echo "🔧 Setting up Zsh integration..."
ZSH_THEME_FILE="$HOME/.config/zsh/18-theme-integration.zsh"
if [[ -f "$ZSH_THEME_FILE" ]]; then
    # Update existing file
    if ! grep -q "Universal Theme System Integration" "$ZSH_THEME_FILE"; then
        cat >> "$ZSH_THEME_FILE" << 'EOF'

# === UNIVERSAL THEME SYSTEM INTEGRATION ===
THEME_SYSTEM_DIR="$HOME/.config/zsh/theme_system"

if [[ -x "$THEME_SYSTEM_DIR/theme" ]]; then
    # Theme management aliases
    alias theme="$THEME_SYSTEM_DIR/theme"
    alias themes="$THEME_SYSTEM_DIR/theme list"
    alias dark="$THEME_SYSTEM_DIR/theme apply gruvbox-dark"
    alias light="$THEME_SYSTEM_DIR/theme apply gruvbox-light"
    alias prismatic="$THEME_SYSTEM_DIR/theme apply prismatic-dark"
    alias mono="$THEME_SYSTEM_DIR/theme apply monochrome-dark"
    alias catppuccin="$THEME_SYSTEM_DIR/theme apply catppuccin-mocha"
    alias nord="$THEME_SYSTEM_DIR/theme apply nord"
    
    # Quick theme switching
    alias theme-select="$THEME_SYSTEM_DIR/theme select"
    alias theme-toggle="$THEME_SYSTEM_DIR/theme toggle"
    alias theme-reload="$THEME_SYSTEM_DIR/theme reload"
    alias theme-status="$THEME_SYSTEM_DIR/theme status"
    
    # Auto-apply current theme on startup (silent)
    "$THEME_SYSTEM_DIR/theme" reload >/dev/null 2>&1 || true
fi
EOF
        echo "  ✓ Updated Zsh integration"
    else
        echo "  ✓ Zsh integration already configured"
    fi
fi

# Apply default theme
echo "🎨 Applying default theme..."
if ./theme apply gruvbox-dark --no-reload >/dev/null 2>&1; then
    echo "  ✓ Default theme applied"
else
    echo "⚠️  Warning: Could not apply default theme"
fi

echo ""
echo "🎉 Setup Complete!"
echo "=================="
echo ""
echo "📋 Quick Start:"
echo "  ./theme list              # List available themes"
echo "  ./theme select            # Interactive theme selector"
echo "  ./theme apply gruvbox-dark # Apply specific theme"
echo "  ./theme toggle            # Toggle light/dark variant"
echo "  ./theme status            # Show current status"
echo ""
echo "🎨 Available Themes:"
./theme list 2>/dev/null || echo "  Run './theme list' to see available themes"
echo ""
echo "📚 Documentation:"
echo "  README.md                 # Overview and features"
echo "  INSTALLATION.md           # Detailed installation guide"
echo "  docs/ZELLIJ_KEYBINDS.md   # Zellij keybind reference"
echo ""
echo "🔄 Next Steps:"
echo "  1. Restart your terminal or run: source ~/.zshrc"
echo "  2. Test theme switching: theme select"
echo "  3. Customize config.json for your setup"
echo "  4. Create custom themes: theme create my-theme"
echo ""
echo "✨ Enjoy your new theme system!"
