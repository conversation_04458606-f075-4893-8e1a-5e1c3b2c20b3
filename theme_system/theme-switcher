#!/usr/bin/env python3
"""
Interactive Theme Switcher with FZF
Designed to be launched as a scratchpad via pyprland
"""

import os
import sys
import json
import subprocess
from pathlib import Path

# Theme system directory
THEME_DIR = Path(__file__).parent
THEMES_DIR = THEME_DIR / "themes"
THEME_FAST = THEME_DIR / "theme-fast"

def get_available_themes():
    """Get list of available themes."""
    themes = []
    if THEMES_DIR.exists():
        for theme_file in THEMES_DIR.glob("*.json"):
            try:
                with open(theme_file) as f:
                    theme_data = json.load(f)
                    name = theme_file.stem
                    description = theme_data.get("description", "")
                    variant = theme_data.get("variant", "")

                    # Format: "theme-name | description | variant"
                    display = f"{name}"
                    if description:
                        display += f" | {description}"
                    if variant:
                        display += f" | {variant}"

                    themes.append((name, display))
            except (json.JSONDecodeError, KeyError):
                continue

    return sorted(themes)

def get_current_theme():
    """Get current theme from state file."""
    state_file = Path.home() / ".config/current-theme"
    if state_file.exists():
        try:
            return state_file.read_text().strip()
        except:
            pass
    return "gruvbox-dark"

def apply_theme(theme_name):
    """Apply selected theme."""
    if THEME_FAST.exists():
        try:
            result = subprocess.run([
                str(THEME_FAST), theme_name
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                print(f"✓ Applied theme: {theme_name}")
                return True
            else:
                print(f"✗ Failed to apply theme: {result.stderr}")
                return False
        except subprocess.TimeoutExpired:
            print("✗ Theme application timed out")
            return False
        except Exception as e:
            print(f"✗ Error applying theme: {e}")
            return False
    else:
        print(f"✗ Theme script not found: {THEME_FAST}")
        return False

def run_fzf_selector():
    """Run FZF theme selector."""
    themes = get_available_themes()
    if not themes:
        print("No themes found!")
        return None

    current_theme = get_current_theme()

    # Prepare FZF input
    fzf_input = []
    for theme_name, display in themes:
        marker = "● " if theme_name == current_theme else "  "
        fzf_input.append(f"{marker}{display}")

    # FZF options - simplified and more robust
    fzf_cmd = [
        "fzf",
        "--height=60%",
        "--layout=reverse",
        "--border=rounded",
        "--prompt=Theme ❯ ",
        f"--header=Select a theme (current: {current_theme})",
        "--bind=ctrl-c:abort",
        "--bind=esc:abort",
        "--bind=tab:down",
        "--bind=shift-tab:up",
        "--no-multi",
        "--cycle"
    ]

    try:
        # Run FZF with error handling
        result = subprocess.run(
            fzf_cmd,
            input="\n".join(fzf_input),
            text=True,
            capture_output=True,
            timeout=30  # Add timeout
        )

        if result.returncode == 0:
            selected = result.stdout.strip()
            if selected and len(selected) > 2:
                # Extract theme name (remove marker and description)
                try:
                    theme_name = selected[2:].split(" | ")[0].strip()
                    if theme_name:
                        return theme_name
                except (IndexError, AttributeError):
                    print(f"Error parsing selection: {selected}")
                    return None
        elif result.returncode == 130:  # Ctrl+C
            print("Selection cancelled")
        elif result.returncode == 1:  # No selection
            print("No theme selected")
        else:
            print(f"FZF failed with code {result.returncode}")
            if result.stderr:
                print(f"Error: {result.stderr}")

    except subprocess.TimeoutExpired:
        print("Selection timed out")
        return None
    except FileNotFoundError:
        print("FZF not found! Please install fzf.")
        return None
    except Exception as e:
        print(f"Error running FZF: {e}")
        return None

    return None

def show_current_theme():
    """Show current theme info."""
    current = get_current_theme()
    theme_file = THEMES_DIR / f"{current}.json"

    print(f"Current theme: {current}")

    if theme_file.exists():
        try:
            with open(theme_file) as f:
                theme_data = json.load(f)

            print(f"Description: {theme_data.get('description', 'N/A')}")
            print(f"Variant: {theme_data.get('variant', 'N/A')}")
            print(f"Author: {theme_data.get('author', 'N/A')}")

        except Exception as e:
            print(f"Error reading theme data: {e}")

def main():
    """Main function."""
    if len(sys.argv) > 1:
        command = sys.argv[1]

        if command == "list":
            themes = get_available_themes()
            current = get_current_theme()

            print("Available themes:")
            for theme_name, display in themes:
                marker = "●" if theme_name == current else " "
                print(f"  {marker} {display}")
            return

        elif command == "current":
            show_current_theme()
            return

        elif command == "apply" and len(sys.argv) > 2:
            theme_name = sys.argv[2]
            if apply_theme(theme_name):
                sys.exit(0)
            else:
                sys.exit(1)

        elif command == "help":
            print("Theme Switcher")
            print("Usage:")
            print("  theme-switcher           - Interactive selection")
            print("  theme-switcher list      - List all themes")
            print("  theme-switcher current   - Show current theme")
            print("  theme-switcher apply <theme> - Apply specific theme")
            print("  theme-switcher help      - Show this help")
            return

    # Interactive mode
    print("🎨 Theme Switcher")
    print("=" * 40)

    selected_theme = run_fzf_selector()

    if selected_theme:
        print(f"\nApplying theme: {selected_theme}")
        if apply_theme(selected_theme):
            print("Theme applied successfully!")
        else:
            print("Failed to apply theme!")
            sys.exit(1)
    else:
        print("No theme selected")

if __name__ == "__main__":
    main()
