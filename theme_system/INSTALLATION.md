# 🚀 Installation Guide

Complete setup guide for the Universal Theme System.

## 📋 Prerequisites

### Required Dependencies
```bash
# Python 3.8+
python3 --version

# Package manager (choose one)
pip install --user pipx  # Recommended
# OR
sudo pacman -S python-pip  # Arch Linux
# OR  
sudo apt install python3-pip  # Ubuntu/Debian

# FZF for interactive selection
sudo pacman -S fzf  # Arch Linux
# OR
sudo apt install fzf  # Ubuntu/Debian
# OR
brew install fzf  # macOS
```

### Optional Dependencies
```bash
# For GTK theme support
sudo pacman -S gtk3 gtk4

# For Hyprland support
sudo pacman -S hyprland

# For clipboard integration
sudo pacman -S wl-clipboard  # Wayland
# OR
sudo pacman -S xclip  # X11
```

## 🔧 Installation

### 1. Clone/Copy Theme System
```bash
# Navigate to your zsh config directory
cd ~/.config/zsh

# If you already have the theme_system directory, you're ready!
# Otherwise, create it:
mkdir -p theme_system
```

### 2. Install Python Dependencies
```bash
cd ~/.config/zsh/theme_system
pip install --user -r requirements.txt

# OR using pipx (recommended for isolation)
pipx install -r requirements.txt
```

### 3. Make CLI Executable
```bash
chmod +x theme
```

### 4. Add to PATH (Optional)
```bash
# Add to your ~/.zshrc or ~/.bashrc
export PATH="$HOME/.config/zsh/theme_system:$PATH"

# OR create a symlink
sudo ln -s ~/.config/zsh/theme_system/theme /usr/local/bin/theme
```

## ⚙️ Configuration

### 1. Initialize Configuration
```bash
# Run the theme system once to create default config
./theme status
```

### 2. Configure Applications
Edit `~/.config/zsh/theme_system/config.json`:

```json
{
  "current_theme": "gruvbox-dark",
  "applications": {
    "foot": {
      "enabled": true,
      "config_path": "~/.config/foot/foot.ini",
      "reload_method": "signal"
    },
    "hyprland": {
      "enabled": true,
      "config_path": "~/.config/hypr/hyprland.conf",
      "reload_method": "hyprctl"
    },
    "zellij": {
      "enabled": true,
      "config_path": "~/.config/zellij/config.kdl",
      "reload_method": "auto"
    },
    "gtk": {
      "enabled": true,
      "config_path": "~/.config/gtk-3.0/gtk.css",
      "reload_method": "gsettings"
    },
    "hyprpanel": {
      "enabled": false,
      "config_path": "~/.config/hyprpanel/style.css",
      "reload_method": "restart"
    }
  }
}
```

### 3. Backup Existing Configs
```bash
# Backup your current configs before first use
cp ~/.config/foot/foot.ini ~/.config/foot/foot.ini.backup
cp ~/.config/zellij/config.kdl ~/.config/zellij/config.kdl.backup
cp ~/.config/hypr/hyprland.conf ~/.config/hypr/hyprland.conf.backup
```

## 🎨 First Use

### 1. List Available Themes
```bash
./theme list
```

### 2. Apply Your First Theme
```bash
# Apply a specific theme
./theme apply gruvbox-dark

# OR use interactive selector
./theme select
```

### 3. Test Hot Reload
```bash
# Switch to light variant
./theme apply gruvbox-light

# Toggle between variants
./theme toggle

# Reload current theme
./theme reload
```

## 🔗 Integration with Existing Zsh Config

### 1. Add Theme Aliases
Add to your `~/.config/zsh/18-theme-integration.zsh`:

```bash
# Universal Theme System Integration
THEME_SYSTEM_DIR="$HOME/.config/zsh/theme_system"

if [[ -x "$THEME_SYSTEM_DIR/theme" ]]; then
    # Theme management aliases
    alias theme="$THEME_SYSTEM_DIR/theme"
    alias themes="$THEME_SYSTEM_DIR/theme list"
    alias dark="$THEME_SYSTEM_DIR/theme apply gruvbox-dark"
    alias light="$THEME_SYSTEM_DIR/theme apply gruvbox-light"
    alias prismatic="$THEME_SYSTEM_DIR/theme apply prismatic-dark"
    alias mono="$THEME_SYSTEM_DIR/theme apply monochrome-dark"
    
    # Quick theme switching
    alias theme-select="$THEME_SYSTEM_DIR/theme select"
    alias theme-toggle="$THEME_SYSTEM_DIR/theme toggle"
    alias theme-reload="$THEME_SYSTEM_DIR/theme reload"
    alias theme-status="$THEME_SYSTEM_DIR/theme status"
fi
```

### 2. Auto-apply Theme on Startup
Add to your `~/.config/zsh/00-environment.zsh`:

```bash
# Auto-apply current theme on shell startup
if [[ -x "$HOME/.config/zsh/theme_system/theme" ]]; then
    "$HOME/.config/zsh/theme_system/theme" reload >/dev/null 2>&1
fi
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Python Module Not Found
```bash
# Check Python path
python3 -c "import sys; print(sys.path)"

# Install in user directory
pip install --user -r requirements.txt

# OR use virtual environment
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

#### 2. FZF Not Working
```bash
# Install FZF
sudo pacman -S fzf  # Arch
sudo apt install fzf  # Ubuntu

# Test FZF
echo -e "option1\noption2\noption3" | fzf
```

#### 3. Hot Reload Not Working
```bash
# Check if applications are running
pgrep foot
pgrep zellij
pgrep hyprland

# Test manual reload
hyprctl reload  # Hyprland
pkill -SIGUSR1 foot  # Foot
```

#### 4. Permission Errors
```bash
# Make sure script is executable
chmod +x ~/.config/zsh/theme_system/theme

# Check config file permissions
ls -la ~/.config/zsh/theme_system/config.json
```

### Getting Help

```bash
# Show CLI help
./theme --help

# Show command-specific help
./theme apply --help
./theme create --help

# Check system status
./theme status
```

## 🔄 Updates

### Updating the Theme System
```bash
cd ~/.config/zsh/theme_system

# Backup your custom themes
cp themes/*.json ~/theme_backups/

# Update Python dependencies
pip install --user --upgrade -r requirements.txt

# Test the update
./theme status
```

### Adding New Themes
```bash
# Create from template
./theme create my-custom-theme --base gruvbox-dark

# Import from file
./theme import ~/Downloads/awesome-theme.json

# Export for sharing
./theme export my-custom-theme > my-theme.json
```

## ✅ Verification

After installation, verify everything works:

```bash
# 1. Check theme system status
./theme status

# 2. List available themes
./theme list

# 3. Test theme switching
./theme apply gruvbox-dark
./theme apply gruvbox-light

# 4. Test interactive selector
./theme select

# 5. Test hot reload
./theme reload
```

If all commands work without errors, your installation is complete! 🎉

## 📚 Next Steps

- Read [THEMES.md](THEMES.md) for theme customization
- Check [docs/API.md](docs/API.md) for advanced usage
- See [docs/TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md) for common issues
