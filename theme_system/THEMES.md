# 🎨 Theme Reference Guide

Complete guide to themes, customization, and creation in the Universal Theme System.

## 🌈 **Available Themes**

### **Popular Color Schemes**

#### **Gruvbox Family**
- **gruvbox-dark** - Retro groove with warm, earthy dark tones
- **gruvbox-light** - Retro groove with warm, light tones

#### **Catppuccin Family** 
- **catppuccin-mocha** - Soothing pastel dark theme
- **catppuccin-macchiato** - Medium dark variant
- **catppuccin-frappe** - Cool dark variant  
- **catppuccin-latte** - Light variant

#### **Nord Family**
- **nord** - Arctic, north-bluish dark palette
- **nord-light** - Light variant with blue accents

#### **Specialty Themes**
- **dracula** - Dark theme with purple accents
- **tokyo-night** - Dark theme with neon accents
- **tokyo-night-light** - Light variant
- **solarized-dark** - Classic dark scheme
- **solarized-light** - Classic light scheme
- **one-dark** - <PERSON><PERSON>'s dark theme
- **one-light** - <PERSON><PERSON>'s light theme

#### **Monochrome Themes**
- **monochrome-dark** - Pure black and white
- **monochrome-light** - Pure white and black

#### **Custom Holographic Themes**
- **prismatic-dark** - Metallic silver with blue/purple iridescence
- **prismatic-light** - Light silver with sky blue accents

## 🎯 **Theme Structure**

Each theme is defined in JSON format with the following structure:

```json
{
  "name": "Theme Display Name",
  "description": "Theme description",
  "author": "Theme Author",
  "variant": "dark|light",
  "variants": ["theme-dark", "theme-light"],
  "colors": {
    "bg": "#background",
    "fg": "#foreground",
    "red": "#red",
    "green": "#green",
    "blue": "#blue",
    "yellow": "#yellow",
    "purple": "#purple",
    "aqua": "#cyan",
    "orange": "#orange",
    "gray": "#gray"
  },
  "terminal": {
    "black": "#000000",
    "red": "#ff0000",
    "green": "#00ff00",
    "yellow": "#ffff00",
    "blue": "#0000ff",
    "magenta": "#ff00ff",
    "cyan": "#00ffff",
    "white": "#ffffff",
    "bright_black": "#808080",
    "bright_red": "#ff8080",
    "bright_green": "#80ff80",
    "bright_yellow": "#ffff80",
    "bright_blue": "#8080ff",
    "bright_magenta": "#ff80ff",
    "bright_cyan": "#80ffff",
    "bright_white": "#ffffff"
  },
  "ui": {
    "background": "#background",
    "foreground": "#foreground",
    "cursor": "#cursor",
    "selection": "#selection",
    "highlight": "#highlight",
    "border": "#border",
    "accent": "#accent",
    "warning": "#warning",
    "error": "#error",
    "success": "#success",
    "info": "#info"
  }
}
```

## 🛠️ **Creating Custom Themes**

### **Method 1: From Template**
```bash
# Create new theme based on existing one
./theme create my-awesome-theme --base gruvbox-dark

# Edit the generated file
nvim themes/my-awesome-theme.json

# Apply your new theme
./theme apply my-awesome-theme
```

### **Method 2: From Scratch**
```bash
# Copy template
cp themes/gruvbox-dark.json themes/my-theme.json

# Edit all colors and metadata
nvim themes/my-theme.json

# Test the theme
./theme preview my-theme
./theme apply my-theme
```

### **Method 3: Import External**
```bash
# Import from file
./theme import ~/Downloads/awesome-theme.json

# Export for sharing
./theme export my-theme > my-theme.json
```

## 🎨 **Color Guidelines**

### **Choosing Colors**
1. **Background/Foreground**: High contrast for readability
2. **Terminal Colors**: Follow standard ANSI color meanings
3. **UI Colors**: Consistent with overall theme mood
4. **Accent Colors**: Used sparingly for highlights

### **Color Harmony**
- **Monochromatic**: Variations of single hue
- **Analogous**: Adjacent colors on color wheel  
- **Complementary**: Opposite colors for contrast
- **Triadic**: Three evenly spaced colors

### **Accessibility**
- Minimum 4.5:1 contrast ratio for text
- Test with colorblind simulators
- Avoid red/green only distinctions
- Provide sufficient brightness differences

## 🔧 **Advanced Customization**

### **Application-Specific Colors**
Some applications support extended color definitions:

```json
{
  "foot": {
    "url_color": "#0087ff",
    "search_highlight": "#ffff00",
    "jump_label": "#ff0000"
  },
  "zellij": {
    "tab_active": "#ffffff",
    "tab_inactive": "#808080",
    "pane_border": "#404040"
  },
  "hyprland": {
    "window_border": "#0087ff",
    "window_border_inactive": "#404040"
  }
}
```

### **Dynamic Colors**
Use color functions for variations:

```json
{
  "colors": {
    "bg": "#282828",
    "bg_light": "#3c3836",
    "bg_lighter": "#504945"
  }
}
```

### **Theme Variants**
Create multiple variants of the same theme:

```json
{
  "name": "My Theme Dark",
  "variants": ["my-theme-dark", "my-theme-light", "my-theme-high-contrast"],
  "base_theme": "my-theme-dark"
}
```

## 🎯 **Theme Testing**

### **Preview Before Applying**
```bash
# Preview colors in terminal
./theme preview my-theme

# Test without hot reload
./theme apply my-theme --no-reload

# Quick toggle test
./theme toggle
```

### **Testing Checklist**
- [ ] Text readability in terminal
- [ ] Syntax highlighting in editor
- [ ] UI element visibility
- [ ] Selection contrast
- [ ] Error/warning colors
- [ ] Link colors
- [ ] Border visibility

### **Application Testing**
Test your theme across all applications:

```bash
# Test in different terminals
foot -e ./theme apply my-theme
alacritty -e ./theme apply my-theme

# Test Zellij modes
zellij # Then Alt+s for scroll mode

# Test Hyprland
hyprctl reload
```

## 📚 **Theme Examples**

### **Minimal Dark Theme**
```json
{
  "name": "Minimal Dark",
  "variant": "dark",
  "colors": {
    "bg": "#000000",
    "fg": "#ffffff",
    "red": "#ff5555",
    "green": "#50fa7b",
    "yellow": "#f1fa8c",
    "blue": "#bd93f9",
    "purple": "#ff79c6",
    "aqua": "#8be9fd",
    "orange": "#ffb86c",
    "gray": "#6272a4"
  }
}
```

### **Warm Light Theme**
```json
{
  "name": "Warm Light",
  "variant": "light",
  "colors": {
    "bg": "#fdf6e3",
    "fg": "#657b83",
    "red": "#dc322f",
    "green": "#859900",
    "yellow": "#b58900",
    "blue": "#268bd2",
    "purple": "#d33682",
    "aqua": "#2aa198",
    "orange": "#cb4b16",
    "gray": "#93a1a1"
  }
}
```

## 🔄 **Theme Management**

### **Organizing Themes**
```bash
# List by variant
./theme list | grep dark
./theme list | grep light

# Show detailed info
./theme list --detailed

# Current status
./theme status
```

### **Backup and Restore**
```bash
# Backup all themes
cp -r themes/ ~/theme-backup/

# Export specific theme
./theme export gruvbox-dark > gruvbox-backup.json

# Restore theme
./theme import gruvbox-backup.json
```

### **Sharing Themes**
```bash
# Export for sharing
./theme export my-awesome-theme > my-awesome-theme.json

# Share the JSON file
# Others can import with:
./theme import my-awesome-theme.json
```

## 🎨 **Color Tools**

### **Recommended Tools**
- **Coolors.co** - Color palette generator
- **Adobe Color** - Professional color tools
- **Paletton** - Color scheme designer
- **Contrast Checker** - Accessibility testing

### **Terminal Color Testing**
```bash
# Test 256 colors
for i in {0..255}; do echo -e "\e[38;5;${i}mColor $i\e[0m"; done

# Test true color
awk 'BEGIN{
    s="/\\/\\/\\/\\/\\"; s=s s s s s s s s;
    for (colnum = 0; colnum<77; colnum++) {
        r = 255-(colnum*255/76);
        g = (colnum*510/76);
        b = (colnum*255/76);
        if (g>255) g = 510-g;
        printf "\033[48;2;%d;%d;%dm", r,g,b;
        printf "\033[38;2;%d;%d;%dm", 255-r,255-g,255-b;
        printf "%s\033[0m", substr(s,colnum+1,1);
    }
    printf "\n";
}'
```

## 🔗 **Integration**

### **With Existing Themes**
The theme system can coexist with:
- Terminal theme switchers
- Desktop environment themes
- Application-specific themes

### **Export to Other Formats**
```bash
# Convert to Alacritty format
./theme export gruvbox-dark | jq '.terminal' > alacritty-gruvbox.yml

# Convert to Kitty format  
./theme export gruvbox-dark | python3 convert-to-kitty.py > kitty-gruvbox.conf
```

## 📖 **Resources**

### **Color Theory**
- [Adobe Color Theory](https://color.adobe.com/create/color-wheel)
- [Material Design Colors](https://material.io/design/color/)
- [Web Accessibility Guidelines](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html)

### **Existing Themes**
- [Base16 Themes](https://github.com/chriskempson/base16)
- [Terminal Sexy](https://terminal.sexy/)
- [Gogh Terminal Themes](https://mayccoll.github.io/Gogh/)

### **Tools**
- [Themera](https://github.com/GideonWolfe/Themera)
- [Pywal](https://github.com/dylanaraps/pywal)
- [Flavours](https://github.com/Misterio77/flavours)
