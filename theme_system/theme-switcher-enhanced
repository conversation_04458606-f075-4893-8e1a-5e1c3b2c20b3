#!/usr/bin/env zsh

# Enhanced Theme Switcher with Color Palette Preview
# Interactive theme selection with visual color preview

THEME_SCRIPT="$HOME/.config/zsh/theme_system/theme-fast"
CURRENT_THEME_FILE="$HOME/.config/current-theme"

# Color definitions for themes (extracted from theme-fast)
declare -A THEME_COLORS
THEME_COLORS[gruvbox-dark]="#282828 #ebdbb2 #cc241d #98971a #d79921 #d65d0e #b16286 #689d6a"
THEME_COLORS[gruvbox-light]="#fbf1c7 #3c3836 #cc241d #98971a #d79921 #d65d0e #b16286 #689d6a"
THEME_COLORS[solarized-dark]="#002b36 #839496 #dc322f #859900 #b58900 #268bd2 #d33682 #2aa198"
THEME_COLORS[solarized-light]="#fdf6e3 #657b83 #dc322f #859900 #b58900 #268bd2 #d33682 #2aa198"
THEME_COLORS[monokai]="#272822 #f8f8f2 #f92672 #a6e22e #f4bf75 #66d9ef #ae81ff #a1efe4"
THEME_COLORS[catppuccin-mocha]="#1e1e2e #cdd6f4 #f38ba8 #a6e3a1 #f9e2af #89b4fa #cba6f7 #94e2d5"
THEME_COLORS[catppuccin-macchiato]="#24273a #cad3f5 #ed8796 #a6da95 #eed49f #8aadf4 #c6a0f6 #91d7e3"
THEME_COLORS[catppuccin-frappe]="#303446 #c6d0f5 #e78284 #a6d189 #e5c890 #8caaee #ca9ee6 #81c8be"
THEME_COLORS[catppuccin-latte]="#eff1f5 #4c4f69 #d20f39 #40a02b #df8e1d #1e66f5 #8839ef #179299"
THEME_COLORS[tokyo-night]="#1a1b26 #c0caf5 #f7768e #9ece6a #e0af68 #7aa2f7 #bb9af7 #7dcfff"
THEME_COLORS[tokyo-night-light]="#d5d6db #565a6e #8c4351 #33635c #8f5e15 #34548a #5a4a78 #0f4b6e"
THEME_COLORS[tokyo-night-storm]="#24283b #c0caf5 #f7768e #9ece6a #e0af68 #7aa2f7 #bb9af7 #7dcfff"
THEME_COLORS[dracula]="#282a36 #E0E1DF #ff5555 #50fa7b #f1fa8c #bd93f9 #ff79c6 #8be9fd"
THEME_COLORS[dracula-soft]="#21222c #E0E1DF #ff6e6e #69ff94 #ffffa5 #d6acff #ff92df #a4ffff"
THEME_COLORS[nord]="#2e3440 #d8dee9 #bf616a #a3be8c #ebcb8b #81a1c1 #b48ead #88c0d0"
THEME_COLORS[nord-light]="#eceff4 #2e3440 #bf616a #a3be8c #ebcb8b #5e81ac #b48ead #88c0d0"
THEME_COLORS[one-dark]="#282c34 #abb2bf #e06c75 #98c379 #e5c07b #61afef #c678dd #56b6c2"
THEME_COLORS[one-light]="#E0E1DF #383a42 #e45649 #50a14f #c18401 #4078f2 #a626a4 #0184bc"
THEME_COLORS[material-dark]="#212121 #E0E1DF #f07178 #c3e88d #ffcb6b #82aaff #c792ea #89ddff"
THEME_COLORS[material-light]="#E0E1DF #90a4ae #e53935 #91b859 #ffb62c #6182b8 #7c4dff #39adb5"
THEME_COLORS[material-ocean]="#0f111a #8f93a2 #ff5370 #c3e88d #ffcb6b #82aaff #c792ea #89ddff"
THEME_COLORS[github-dark]="#0d1117 #c9d1d9 #ff7b72 #7ee787 #ffa657 #79c0ff #d2a8ff #a5f3fc"
THEME_COLORS[github-light]="#E0E1DF #24292f #cf222e #116329 #4d2d00 #0969da #8250df #1b7c83"
THEME_COLORS[ayu-dark]="#0a0e14 #b3b1ad #f07178 #bae67e #ffd580 #73d0ff #d4bfff #95e6cb"
THEME_COLORS[ayu-light]="#E0E1DF #5c6166 #f51818 #86b300 #f2ae49 #399ee6 #a37acc #4cbf99"
THEME_COLORS[ayu-mirage]="#1f2430 #cbccc6 #f28779 #bae67e #ffd580 #73d0ff #d4bfff #95e6cb"
THEME_COLORS[everforest-dark]="#2d353b #d3c6aa #e67e80 #a7c080 #dbbc7f #7fbbb3 #d699b6 #83c092"
THEME_COLORS[everforest-light]="#fdf6e3 #5c6a72 #f85552 #8da101 #dfa000 #3a94c5 #df69ba #35a77c"
THEME_COLORS[rose-pine]="#191724 #e0def4 #eb6f92 #31748f #f6c177 #9ccfd8 #c4a7e7 #ebbcba"
THEME_COLORS[rose-pine-moon]="#232136 #e0def4 #eb6f92 #3e8fb0 #f6c177 #9ccfd8 #c4a7e7 #ea9a97"
THEME_COLORS[rose-pine-dawn]="#faf4ed #575279 #b4637a #286983 #ea9d34 #56949f #907aa9 #d7827e"
THEME_COLORS[prismatic-dark]="#0d0d0d #E0E1DF #ff6b6b #4ecdc4 #e6b800 #6ba6cd #7c4dff #00d4aa"
THEME_COLORS[prismatic-light]="#f5f5f5 #2d2d2d #d63031 #00b894 #b8860b #4682b4 #5e35b1 #00acc1"
THEME_COLORS[steel-dark]="#1c1c1c #c7c7c7 #e74c3c #27ae60 #d68910 #5dade2 #8e44ad #48c9b0"
THEME_COLORS[obsidian]="#000000 #E0E1DF #ff4757 #2ed573 #e6b800 #3742fa #7c4dff #26d0ce"
THEME_COLORS[platinum]="#E0E1DF #000000 #c0392b #27ae60 #b8860b #2980b9 #6a1b9a #17a2b8"
THEME_COLORS[copper]="#2c1810 #d4af37 #cd853f #8fbc8f #b8860b #4682b4 #8e44ad #20b2aa"
THEME_COLORS[kanagawa]="#1f1f28 #dcd7ba #c34043 #76946a #c0a36e #7e9cd8 #957fb8 #6a9589"
THEME_COLORS[nightfox]="#192330 #cdcecf #c94f6d #81b29a #dbc074 #719cd6 #9d79d6 #63cdcf"
THEME_COLORS[carbonfox]="#161616 #f2f4f8 #ee5396 #25be6a #08bdba #78a9ff #be95ff #33b1ff"
THEME_COLORS[tokyonight-moon]="#222436 #c8d3f5 #ff757f #c3e88d #ffc777 #82aaff #c099ff #86e1fc"
THEME_COLORS[chocolate-gruvbox]="#2b1d0e #d4be98 #ea6962 #a9b665 #e78a4e #d8a657 #d3869b #89b482"
THEME_COLORS[monokai-pro]="#2d2a2e #fcfcfa #ff6188 #a9dc76 #ffd866 #fc9867 #ab9df2 #78dce8"
THEME_COLORS[palenight]="#292d3e #a6accd #f07178 #c3e88d #ffcb6b #82aaff #c792ea #89ddff"
THEME_COLORS[synthwave]="#2a2139 #e0e0e0 #fe4450 #72f1b8 #e6b800 #03edf9 #8b5cf6 #09f7a0"
THEME_COLORS[horizon]="#1c1e26 #e0e0e0 #e95678 #29d398 #d68910 #26bbd9 #8e44ad #59e3e3"
THEME_COLORS[shades-of-purple]="#2d2b55 #a599e9 #d90429 #3ad900 #e6b800 #6943ff #7c4dff #00d9ff"
THEME_COLORS[cobalt2]="#193549 #ffffff #ff0000 #38de21 #e6b800 #1460d2 #8e44ad #00bbbb"
THEME_COLORS[night-owl]="#011627 #d6deeb #ef5350 #22da6e #addb67 #82aaff #c792ea #21c7a8"
THEME_COLORS[winter-is-coming]="#0e293f #ffffff #ff6b68 #5de4c7 #fffac2 #add7ff #91ddff #87dfeb"
THEME_COLORS[andromeda]="#262a33 #f7f7f7 #f92672 #a6e22e #fd971f #66d9ef #ae81ff #a1efe4"
THEME_COLORS[atom-one-dark-pro]="#1e2127 #e06c75 #e06c75 #98c379 #d19a66 #61afef #c678dd #56b6c2"
THEME_COLORS[darcula]="#2b2b2b #a9b7c6 #ff6b68 #a8c023 #ffc66d #6897bb #cc7832 #629755"

# Get current theme
get_current_theme() {
    if [[ -f "$CURRENT_THEME_FILE" ]]; then
        cat "$CURRENT_THEME_FILE"
    else
        echo "gruvbox-dark"
    fi
}

# Get available themes
get_themes() {
    "$THEME_SCRIPT" list 2>/dev/null | sed 's/ (current)$//'
}

# Convert hex to RGB for terminal colors
hex_to_rgb() {
    local hex="$1"
    hex="${hex#\#}"
    printf "%d;%d;%d" "0x${hex:0:2}" "0x${hex:2:2}" "0x${hex:4:2}"
}

# Create color block
color_block() {
    local color="$1"
    local rgb=$(hex_to_rgb "$color")
    printf "\033[48;2;${rgb}m  \033[0m"
}

# Preview theme function with color palette
preview_theme() {
    local theme="$1"
    echo "Theme: $theme"
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

    # Show theme description
    case "$theme" in
        gruvbox-dark) echo "🌲 Gruvbox Dark - Retro groove color scheme with orange accents" ;;
        gruvbox-light) echo "🌻 Gruvbox Light - Retro groove color scheme with orange accents" ;;
        solarized-dark) echo "🌅 Solarized Dark - Precision colors for machines and people" ;;
        solarized-light) echo "☀️  Solarized Light - Precision colors for machines and people" ;;
        monokai) echo "🎯 Monokai - Sublime Text's original color scheme" ;;
        catppuccin-mocha) echo "🌙 Catppuccin Mocha - Soothing pastel theme for the high-spirited" ;;
        catppuccin-macchiato) echo "☕ Catppuccin Macchiato - Warm and cozy pastel theme" ;;
        catppuccin-frappe) echo "🥤 Catppuccin Frappé - Cool and refreshing pastel theme" ;;
        catppuccin-latte) echo "🥛 Catppuccin Latte - Light and creamy pastel theme" ;;
        tokyo-night) echo "🌃 Tokyo Night - A clean, dark theme inspired by Tokyo's night" ;;
        tokyo-night-light) echo "🌅 Tokyo Night Light - Light variant of Tokyo Night" ;;
        tokyo-night-storm) echo "⛈️  Tokyo Night Storm - Stormy variant of Tokyo Night" ;;
        dracula) echo "🧛 Dracula - Dark theme with vibrant colors" ;;
        dracula-soft) echo "🦇 Dracula Soft - Softer variant of Dracula theme" ;;
        nord) echo "❄️  Nord - Arctic, north-bluish color palette" ;;
        nord-light) echo "🏔️  Nord Light - Light variant of Nord theme" ;;
        one-dark) echo "⚫ One Dark - Atom's iconic One Dark theme" ;;
        one-light) echo "⚪ One Light - Atom's iconic One Light theme" ;;
        material-dark) echo "🎨 Material Dark - Google's Material Design dark theme" ;;
        material-light) echo "🎨 Material Light - Google's Material Design light theme" ;;
        material-ocean) echo "🌊 Material Ocean - Deep ocean variant of Material theme" ;;
        github-dark) echo "🐙 GitHub Dark - GitHub's dark theme" ;;
        github-light) echo "🐱 GitHub Light - GitHub's light theme" ;;
        ayu-dark) echo "🌑 Ayu Dark - Simple theme with bright colors" ;;
        ayu-light) echo "🌕 Ayu Light - Simple light theme with bright colors" ;;
        ayu-mirage) echo "🌌 Ayu Mirage - Elegant mirage variant of Ayu" ;;
        everforest-dark) echo "🌲 Everforest Dark - Comfortable green forest theme" ;;
        everforest-light) echo "🌳 Everforest Light - Comfortable light forest theme" ;;
        rose-pine) echo "🌹 Rose Pine - All natural pine, faux fur and a bit of soho vibes" ;;
        rose-pine-moon) echo "🌙 Rose Pine Moon - Moonlit variant of Rose Pine" ;;
        rose-pine-dawn) echo "🌅 Rose Pine Dawn - Dawn variant of Rose Pine" ;;
        prismatic-dark) echo "💎 Prismatic Dark - High contrast dark theme with sky blue" ;;
        prismatic-light) echo "✨ Prismatic Light - High contrast light theme" ;;
        steel-dark) echo "⚙️  Steel Dark - Metallic gray theme with blue accents" ;;
        obsidian) echo "🖤 Obsidian - Maximum contrast black/white theme" ;;
        platinum) echo "🤍 Platinum - High contrast light theme" ;;
        copper) echo "🟤 Copper - Warm metallic theme with golden tones" ;;
        kanagawa) echo "🎋 Kanagawa - Inspired by Katsushika Hokusai's famous painting" ;;
        nightfox) echo "🦊 Nightfox - Highly customizable theme with vibrant colors" ;;
        carbonfox) echo "⚫ Carbon Fox - Dark carbon variant of Nightfox" ;;
        tokyonight-moon) echo "🌙 Tokyo Night Moon - Moonlit variant of Tokyo Night" ;;
        chocolate-gruvbox) echo "🍫 Chocolate Gruvbox - Warm, chocolatey variant of Gruvbox" ;;
        monokai-pro) echo "🎯 Monokai Pro - Enhanced version of the classic Monokai" ;;
        palenight) echo "🌌 Palenight - Material theme with a darker twist" ;;
        synthwave) echo "🌈 Synthwave - Retro 80s neon cyberpunk theme" ;;
        horizon) echo "🌅 Horizon - Warm and colorful theme inspired by sunsets" ;;
        shades-of-purple) echo "💜 Shades of Purple - Professional purple-based theme" ;;
        cobalt2) echo "🔵 Cobalt2 - Classic blue terminal theme" ;;
        night-owl) echo "🦉 Night Owl - Dark theme for night owls" ;;
        winter-is-coming) echo "❄️ Winter is Coming - Cool blue winter theme" ;;
        andromeda) echo "🌌 Andromeda - Space-inspired dark theme" ;;
        atom-one-dark-pro) echo "⚛️ Atom One Dark Pro - Enhanced Atom One Dark" ;;
        darcula) echo "🖤 Darcula - IntelliJ's iconic dark theme" ;;
        *) echo "🎨 $theme" ;;
    esac

    echo ""

    # Show color palette if available
    if [[ -n "${THEME_COLORS[$theme]}" ]]; then
        local colors=(${THEME_COLORS[$theme]})
        echo "Color Palette:"
        echo -n "BG: "; color_block "${colors[0]}"; echo " ${colors[0]}"
        echo -n "FG: "; color_block "${colors[1]}"; echo " ${colors[1]}"
        echo -n "Red: "; color_block "${colors[2]}"; echo " ${colors[2]}"
        echo -n "Green: "; color_block "${colors[3]}"; echo " ${colors[3]}"
        echo -n "Yellow: "; color_block "${colors[4]}"; echo " ${colors[4]}"
        echo -n "Blue: "; color_block "${colors[5]}"; echo " ${colors[5]}"
        echo -n "Purple: "; color_block "${colors[6]}"; echo " ${colors[6]}"
        echo -n "Cyan: "; color_block "${colors[7]}"; echo " ${colors[7]}"
        echo ""
        echo "Full Palette: $(for color in "${colors[@]}"; do color_block "$color"; done)"
    fi

    echo ""
    echo "Press Enter to apply this theme"
}

# Main function
main() {
    if ! command -v fzf >/dev/null 2>&1; then
        echo "Error: fzf is required but not installed."
        exit 1
    fi

    current_theme=$(get_current_theme)

    # Use fzf to select theme
    selected_theme=$(get_themes | fzf \
        --prompt="🎨 Select Theme: " \
        --header="Current: $current_theme | Use ↑↓ to navigate, Enter to apply" \
        --preview="$0 preview {}" \
        --preview-window=right:60% \
        --height=90% \
        --border=rounded \
        --cycle \
        --color="bg+:#313244,bg:#1e1e2e,spinner:#f5e0dc,hl:#f38ba8" \
        --color="fg:#cdd6f4,header:#f38ba8,info:#cba6f7,pointer:#f5e0dc" \
        --color="marker:#f5e0dc,fg+:#cdd6f4,prompt:#cba6f7,hl+:#f38ba8")

    if [[ -n "$selected_theme" ]]; then
        echo "Applying theme: $selected_theme"

        # Apply theme with proper async handling
        "$THEME_SCRIPT" "$selected_theme" &
        local theme_pid=$!

        # Show progress while applying
        echo -n "⏳ Applying theme"
        while kill -0 $theme_pid 2>/dev/null; do
            echo -n "."
            sleep 0.2
        done
        wait $theme_pid
        echo ""

        # Force Hyprpanel reload with better timing
        echo "🔄 Reloading Hyprpanel..."
        pkill -f hyprpanel 2>/dev/null
        sleep 0.5
        hyprpanel &>/dev/null &
        disown

        echo "✅ Theme applied successfully!"
    else
        echo "❌ No theme selected."
    fi
}

# FZF theme selector with color previews
fzf_theme_selector() {
    if ! command -v fzf >/dev/null 2>&1; then
        echo "❌ fzf not found. Please install fzf for enhanced theme selection."
        echo "Falling back to interactive mode..."
        main
        return
    fi

    # Create theme list with color previews
    local themes=()
    for theme in ${(k)THEME_COLORS}; do
        local colors=(${=THEME_COLORS[$theme]})
        local preview_line="$theme"

        # Add color blocks
        for i in {1..8}; do
            if [[ -n "${colors[$i]}" ]]; then
                preview_line+=" $(printf '\033[48;2;%d;%d;%dm  \033[0m' \
                    $((16#${colors[$i]:1:2})) \
                    $((16#${colors[$i]:3:2})) \
                    $((16#${colors[$i]:5:2})))"
            fi
        done

        themes+=("$preview_line")
    done

    # Use fzf to select theme
    local selected=$(printf '%s\n' "${themes[@]}" | \
        fzf --ansi \
            --height=20 \
            --border \
            --prompt="🎨 Select Theme: " \
            --preview="$0 preview {1}" \
            --preview-window=right:50% \
            --header="Use ↑↓ to navigate, Enter to apply, Esc to cancel")

    if [[ -n "$selected" ]]; then
        local theme_name=$(echo "$selected" | awk '{print $1}')
        echo "Applying theme: $theme_name"

        # Apply theme with proper async handling
        "$THEME_SCRIPT" "$theme_name" &
        local theme_pid=$!

        # Show progress while applying
        echo -n "⏳ Applying theme"
        while kill -0 $theme_pid 2>/dev/null; do
            echo -n "."
            sleep 0.2
        done
        wait $theme_pid
        echo ""

        # Force Hyprpanel reload with better timing
        echo "🔄 Reloading Hyprpanel..."
        pkill -f hyprpanel 2>/dev/null
        sleep 0.5
        hyprpanel &>/dev/null &
        disown

        echo "✅ Theme applied successfully!"
    else
        echo "❌ No theme selected."
    fi
}

# Handle different modes
case "$1" in
    "preview")
        preview_theme "$2"
        exit 0
        ;;
    "list")
        get_themes
        exit 0
        ;;
    "fzf")
        fzf_theme_selector
        exit 0
        ;;
    "")
        # No arguments - show FZF interface
        if command -v fzf >/dev/null 2>&1; then
            fzf_theme_selector
        else
            main "$@"
        fi
        ;;
    *)
        # Direct theme application - much faster
        local theme_name="$1"
        echo "🎨 Applying theme: $theme_name"

        # Apply theme directly using theme-fast for speed
        "$THEME_SCRIPT" "$theme_name" &
        local theme_pid=$!

        # Show minimal progress
        echo -n "⏳ "
        while kill -0 $theme_pid 2>/dev/null; do
            echo -n "."
            sleep 0.1
        done
        wait $theme_pid
        echo ""

        # Force Hyprpanel reload with optimized timing
        echo "🔄 Reloading Hyprpanel..."
        pkill -f hyprpanel 2>/dev/null
        sleep 0.3  # Reduced from 0.5 for faster reload
        hyprpanel &>/dev/null &
        disown

        echo "✅ Theme '$theme_name' applied successfully!"
        exit 0
        ;;
esac
