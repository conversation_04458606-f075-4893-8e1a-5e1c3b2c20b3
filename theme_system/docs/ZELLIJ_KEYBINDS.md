# 🎯 Zellij Keybind Reference

Complete guide to Zellij keybinds with proper scroll/search mode separation.

## 🔧 **Fixed Keybind Conflicts**

The theme system includes optimized Zellij keybinds that properly separate scroll and search modes:

### **Mode Separation**
- **Alt+S**: Enter scroll mode (navigation and text selection)
- **Alt+/**: Enter search mode (find text)
- **Ctrl+S**: Alternative scroll mode trigger

## 📋 **Complete Keybind Reference**

### **Normal Mode (Default)**
| Key | Action | Description |
|-----|--------|-------------|
| `Alt+s` | Scroll mode | Enter scroll/navigation mode |
| `Alt+/` | Search mode | Enter text search mode |
| `Alt+e` | Edit search | Enter search with input |
| `Alt+h/j/k/l` | Navigate panes | Vim-style pane navigation |
| `Alt+n` | New pane | Create new pane |
| `Alt+p` | New pane | Alternative new pane |
| `Alt+x` | Close pane | Close current pane |
| `Alt+v` | Vertical split | Split pane vertically |
| `Alt+-` | Horizontal split | Split pane horizontally |
| `Alt+f` | Fullscreen | Toggle pane fullscreen |
| `Alt+q` | Fullscreen | Alternative fullscreen |
| `Alt+t` | New tab | Create new tab |
| `Alt+w` | Close tab | Close current tab |
| `Alt+1-9` | Switch tab | Go to tab 1-9 |
| `Alt+g` | New tab | Alternative new tab |
| `Alt+y` | Copy | Copy selection to clipboard |

### **Scroll Mode (Alt+S)**
| Key | Action | Description |
|-----|--------|-------------|
| `q` / `Esc` / `Alt+s` | Exit | Return to normal mode |
| `j` / `k` | Scroll | Line up/down |
| `h` / `l` | Page scroll | Half page up/down |
| `d` / `u` | Half page | Half page down/up |
| `g` / `G` | Jump | Top/bottom of buffer |
| `v` | Visual select | Start character selection |
| `V` | Line select | Start line selection |
| `Ctrl+v` | Block select | Start block selection |
| `y` | Copy | Copy selection |
| `/` | Search | Enter search mode |
| `?` | Search back | Search backward |
| `w` / `b` | Word nav | Word forward/backward |
| `0` / `$` | Line nav | Line start/end |

### **Search Mode (Alt+/)**
| Key | Action | Description |
|-----|--------|-------------|
| `q` / `Esc` / `Alt+/` | Exit | Return to normal mode |
| `Enter` | Confirm | Apply search and exit |
| `n` | Next | Next search result |
| `N` / `p` | Previous | Previous search result |

### **Text Selection in Scroll Mode**

#### **Visual Character Mode (`v` in scroll mode)**
- `h/j/k/l`: Extend selection
- `w/b`: Select by word
- `0/$`: Select to line start/end
- `y`: Copy selection
- `d`: Delete selection
- `Esc`: Cancel selection

#### **Visual Line Mode (`V` in scroll mode)**
- `j/k`: Extend line selection
- `y`: Copy selected lines
- `d`: Delete selected lines
- `Esc`: Cancel selection

#### **Visual Block Mode (`Ctrl+v` in scroll mode)**
- `h/j/k/l`: Extend block selection
- `y`: Copy block
- `d`: Delete block
- `Esc`: Cancel selection

## 🔄 **Mode Flow Diagram**

```
Normal Mode
    ├── Alt+s → Scroll Mode
    │   ├── v → Visual Character
    │   ├── V → Visual Line  
    │   ├── Ctrl+v → Visual Block
    │   ├── / → Search Mode
    │   └── q/Esc → Normal Mode
    │
    ├── Alt+/ → Search Mode
    │   ├── Enter → Normal Mode
    │   ├── n/N → Navigate results
    │   └── q/Esc → Normal Mode
    │
    └── Alt+e → Enter Search
        └── Enter → Search Mode
```

## 🎯 **Common Workflows**

### **Text Selection and Copy**
1. `Alt+s` - Enter scroll mode
2. Navigate to start position with `h/j/k/l`
3. `v` - Start visual selection
4. Navigate to end position with `h/j/k/l`
5. `y` - Copy to clipboard
6. `q` - Exit scroll mode

### **Search and Navigate**
1. `Alt+/` - Enter search mode
2. Type search term
3. `Enter` - Confirm search
4. `n` - Next result
5. `N` - Previous result
6. `q` - Exit search mode

### **Quick Line Copy**
1. `Alt+s` - Enter scroll mode
2. Navigate to desired line
3. `V` - Select entire line
4. `y` - Copy line
5. `q` - Exit scroll mode

## 🔧 **Customization**

### **Adding Custom Keybinds**
Edit your Zellij config or use the theme system templates:

```kdl
// Custom keybinds in normal mode
normal {
    bind "Alt c" { Copy; }
    bind "Alt r" { SwitchToMode "Resize"; }
}

// Custom keybinds in scroll mode  
scroll {
    bind "Space" { PageScrollDown; }
    bind "Shift Space" { PageScrollUp; }
}
```

### **Alternative Keybind Schemes**
The theme system supports different keybind preferences:

```bash
# Tmux-style keybinds
./theme apply gruvbox-dark --keybind-style tmux

# Vim-style keybinds (default)
./theme apply gruvbox-dark --keybind-style vim

# Custom keybind file
./theme apply gruvbox-dark --keybinds ~/.config/zellij/my-keybinds.kdl
```

## 🐛 **Troubleshooting Keybinds**

### **Alt+S Not Working**
```bash
# Check if Alt key is properly configured
xev | grep -i alt  # X11
wev | grep -i alt  # Wayland

# Test in different terminal
foot -e zellij
```

### **Search Mode Conflicts**
```bash
# Check Zellij config syntax
zellij --check-config

# Reset to default keybinds
cp ~/.config/zellij/config.kdl.backup ~/.config/zellij/config.kdl
```

### **Copy Not Working**
```bash
# Check clipboard tool
which wl-copy  # Wayland
which xclip    # X11

# Test clipboard manually
echo "test" | wl-copy
wl-paste
```

## 📚 **Advanced Usage**

### **Scripted Text Selection**
```bash
# Select and copy specific pattern
zellij action write-chars "Alt+s"
zellij action write-chars "/pattern"
zellij action write-chars "Enter"
zellij action write-chars "v"
zellij action write-chars "n"
zellij action write-chars "y"
```

### **Integration with External Tools**
```bash
# Copy selection to file
# In scroll mode: select text, then y
# Selection is now in clipboard

# Paste to external editor
wl-paste | nvim -

# Search and open in editor
# Use search mode to find text, then copy location
```

## 🎨 **Theme Integration**

The Universal Theme System automatically configures optimal keybinds for each theme:

```bash
# Apply theme with optimized keybinds
./theme apply gruvbox-dark

# Keybinds are automatically configured in:
# ~/.config/zellij/config.kdl
```

All themes include:
- ✅ Proper scroll/search separation
- ✅ Vim-style text selection
- ✅ Clipboard integration
- ✅ Intuitive mode switching
- ✅ No conflicting keybinds

## 🔗 **Related Documentation**

- [Zellij Official Docs](https://zellij.dev/documentation/)
- [Theme System README](../README.md)
- [Installation Guide](../INSTALLATION.md)
- [Troubleshooting](TROUBLESHOOTING.md)
