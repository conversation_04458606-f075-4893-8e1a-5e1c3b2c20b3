# 🎨 Universal Theme System

A comprehensive Python-based theme management system for seamless theming across your entire desktop environment.

## ✨ Features

- **🔄 Hot Reload**: Instantly apply themes without restarting applications
- **🎯 Universal**: Supports Foot, Hyprland, Hyprpanel, Zellij, and GTK
- **🎨 Rich Themes**: 20+ popular color schemes with light/dark variants
- **⚡ Fast**: Python-based with optimized config generation
- **🔧 Extensible**: Easy to add new themes and applications
- **📱 CLI**: FZF-integrated command-line interface

## 🚀 Quick Start

```bash
# Install the theme system
cd ~/.config/zsh/theme_system
python -m pip install -r requirements.txt

# Apply a theme
./theme apply gruvbox-dark

# Interactive theme selector
./theme select

# List all themes
./theme list

# Hot reload current theme
./theme reload
```

## 🎨 Supported Applications

| Application | Config Format | Hot Reload Method |
|-------------|---------------|-------------------|
| **Foot** | INI | SIGUSR1 signal |
| **Hyprland** | Hyprlang | `hyprctl reload` |
| **Hyprpanel** | CSS/JS | File watching |
| **Zellij** | KDL | Auto-reload |
| **GTK** | CSS | `gsettings` |

## 🌈 Available Themes

### Popular Themes
- **Gruvbox** (dark/light)
- **Catppuccin** (mocha/macchiato/frappé/latte)
- **Nord** (dark/light)
- **Dracula** (dark)
- **Solarized** (dark/light)
- **Tokyo Night** (dark/light)
- **One Dark/Light**
- **Monokai** (dark/light)

### Specialty Themes
- **Monochrome** (pure black/white)
- **Prismatic Dark** (holographic metallic)
- **Prismatic Light** (iridescent silver-blue)

### Custom Themes
- Easy JSON-based theme definition
- Support for custom color palettes
- Template-based config generation

## 📁 Directory Structure

```
theme_system/
├── README.md              # This file
├── INSTALLATION.md        # Setup guide
├── THEMES.md             # Theme documentation
├── requirements.txt      # Python dependencies
├── theme                 # Main CLI script
├── src/
│   ├── theme_engine.py   # Core theme engine
│   ├── cli.py           # Command-line interface
│   └── adapters/        # Application adapters
│       ├── foot.py
│       ├── hyprland.py
│       ├── hyprpanel.py
│       ├── zellij.py
│       └── gtk.py
├── themes/              # Theme definitions
│   ├── gruvbox.json
│   ├── catppuccin.json
│   ├── prismatic.json
│   └── ...
├── templates/           # Config templates
│   ├── foot.ini.j2
│   ├── hyprland.conf.j2
│   ├── zellij.kdl.j2
│   └── ...
└── docs/               # Extended documentation
    ├── API.md
    ├── THEMES.md
    └── TROUBLESHOOTING.md
```

## 🔧 Configuration

The theme system uses a central configuration file:

```json
{
  "current_theme": "gruvbox-dark",
  "applications": {
    "foot": {
      "enabled": true,
      "config_path": "~/.config/foot/foot.ini",
      "reload_method": "signal"
    },
    "hyprland": {
      "enabled": true,
      "config_path": "~/.config/hypr/hyprland.conf",
      "reload_method": "hyprctl"
    }
  }
}
```

## 🎯 Usage Examples

```bash
# Apply specific theme
./theme apply catppuccin-mocha

# Switch between light/dark variants
./theme toggle

# Preview theme without applying
./theme preview nord-light

# Create custom theme
./theme create my-theme --base gruvbox-dark

# Export theme for sharing
./theme export gruvbox-dark > my-theme.json

# Import theme
./theme import my-theme.json
```

## 🔄 Hot Reload Details

Each application has optimized hot reload:

- **Foot**: Sends SIGUSR1 to reload config
- **Hyprland**: Uses `hyprctl reload` command
- **Zellij**: Automatic config file watching
- **GTK**: Updates CSS and triggers `gsettings`
- **Hyprpanel**: File watching with graceful restart

## 🛠️ Development

```bash
# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
python -m pytest tests/

# Add new theme
cp themes/template.json themes/my-theme.json
# Edit theme colors and metadata

# Add new application adapter
cp src/adapters/template.py src/adapters/my-app.py
# Implement adapter interface
```

## 📚 Documentation

- [Installation Guide](INSTALLATION.md)
- [Theme Reference](THEMES.md)
- [API Documentation](docs/API.md)
- [Troubleshooting](docs/TROUBLESHOOTING.md)

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Submit pull request

## 📄 License

MIT License - see LICENSE file for details.
