/* GTK Theme - {{ theme.name }} */
/* Generated by Universal Theme System */

@define-color theme_bg_color {{ theme.ui.background }};
@define-color theme_fg_color {{ theme.ui.foreground }};
@define-color theme_base_color {{ theme.ui.background }};
@define-color theme_text_color {{ theme.ui.foreground }};
@define-color theme_selected_bg_color {{ theme.ui.accent }};
@define-color theme_selected_fg_color {{ theme.ui.background }};
@define-color insensitive_bg_color {{ theme.ui.background }};
@define-color insensitive_fg_color {{ theme.colors.bg3 }};
@define-color insensitive_base_color {{ theme.ui.background }};
@define-color theme_unfocused_fg_color {{ theme.colors.fg3 }};
@define-color theme_unfocused_bg_color {{ theme.ui.background }};
@define-color theme_unfocused_base_color {{ theme.ui.background }};
@define-color theme_unfocused_selected_bg_color {{ theme.ui.accent }};
@define-color theme_unfocused_selected_fg_color {{ theme.ui.background }};
@define-color borders {{ theme.ui.border }};
@define-color unfocused_borders {{ theme.ui.border }};

@define-color warning_color {{ theme.ui.warning }};
@define-color error_color {{ theme.ui.error }};
@define-color success_color {{ theme.ui.success }};

/* Window decorations */
.titlebar {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
    border-bottom: 1px solid {{ theme.ui.border }};
}

.titlebar:backdrop {
    background-color: {{ theme.colors.bg1 }};
    color: {{ theme.colors.fg3 }};
}

/* Buttons */
button {
    background-color: {{ theme.colors.bg2 }};
    color: {{ theme.ui.foreground }};
    border: 1px solid {{ theme.ui.border }};
    border-radius: 4px;
}

button:hover {
    background-color: {{ theme.colors.bg3 }};
}

button:active {
    background-color: {{ theme.ui.accent }};
    color: {{ theme.ui.background }};
}

/* Entry fields */
entry {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
    border: 1px solid {{ theme.ui.border }};
    border-radius: 4px;
}

entry:focus {
    border-color: {{ theme.ui.accent }};
}

/* Menus */
menu {
    background-color: {{ theme.colors.bg1 }};
    color: {{ theme.ui.foreground }};
    border: 1px solid {{ theme.ui.border }};
}

menuitem:hover {
    background-color: {{ theme.ui.accent }};
    color: {{ theme.ui.background }};
}

/* Scrollbars */
scrollbar {
    background-color: {{ theme.colors.bg1 }};
}

scrollbar slider {
    background-color: {{ theme.colors.bg3 }};
    border-radius: 4px;
}

scrollbar slider:hover {
    background-color: {{ theme.ui.accent }};
}

/* Notebooks/Tabs */
notebook tab {
    background-color: {{ theme.colors.bg2 }};
    color: {{ theme.ui.foreground }};
    border: 1px solid {{ theme.ui.border }};
}

notebook tab:checked {
    background-color: {{ theme.ui.background }};
    border-bottom-color: {{ theme.ui.accent }};
}

/* Progress bars */
progressbar progress {
    background-color: {{ theme.ui.accent }};
}

progressbar trough {
    background-color: {{ theme.colors.bg2 }};
}

/* Selection */
selection {
    background-color: {{ theme.ui.selection }};
    color: {{ theme.ui.foreground }};
}

/* Tooltips */
tooltip {
    background-color: {{ theme.colors.bg3 }};
    color: {{ theme.ui.foreground }};
    border: 1px solid {{ theme.ui.border }};
    border-radius: 4px;
}

/* Popover */
popover {
    background-color: {{ theme.colors.bg1 }};
    color: {{ theme.ui.foreground }};
    border: 1px solid {{ theme.ui.border }};
    border-radius: 6px;
}

/* Switches */
switch {
    background-color: {{ theme.colors.bg2 }};
    border: 1px solid {{ theme.ui.border }};
}

switch:checked {
    background-color: {{ theme.ui.accent }};
}

switch slider {
    background-color: {{ theme.ui.foreground }};
}

/* Check and radio buttons */
checkbutton check,
radiobutton radio {
    background-color: {{ theme.ui.background }};
    border: 1px solid {{ theme.ui.border }};
}

checkbutton check:checked,
radiobutton radio:checked {
    background-color: {{ theme.ui.accent }};
    color: {{ theme.ui.background }};
}

/* Headerbar */
headerbar {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
    border-bottom: 1px solid {{ theme.ui.border }};
}

headerbar:backdrop {
    background-color: {{ theme.colors.bg1 }};
    color: {{ theme.colors.fg3 }};
}

/* Sidebar */
.sidebar {
    background-color: {{ theme.colors.bg1 }};
    color: {{ theme.ui.foreground }};
}

.sidebar row:selected {
    background-color: {{ theme.ui.accent }};
    color: {{ theme.ui.background }};
}

/* Terminal colors for applications that use them */
.terminal {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
}

/* Status bars */
statusbar {
    background-color: {{ theme.colors.bg1 }};
    color: {{ theme.ui.foreground }};
    border-top: 1px solid {{ theme.ui.border }};
}

/* Separators */
separator {
    background-color: {{ theme.ui.border }};
}

/* Frames */
frame {
    border: 1px solid {{ theme.ui.border }};
}

frame > border {
    border: none;
}

/* Lists */
list {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
}

list row:selected {
    background-color: {{ theme.ui.accent }};
    color: {{ theme.ui.background }};
}

list row:hover {
    background-color: {{ theme.colors.bg2 }};
}

/* Tree views */
treeview {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
}

treeview:selected {
    background-color: {{ theme.ui.accent }};
    color: {{ theme.ui.background }};
}

/* Spinbuttons */
spinbutton {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
    border: 1px solid {{ theme.ui.border }};
}

spinbutton button {
    background-color: {{ theme.colors.bg2 }};
    border: none;
}

spinbutton button:hover {
    background-color: {{ theme.colors.bg3 }};
}

/* Scale/Slider */
scale {
    background-color: {{ theme.colors.bg2 }};
}

scale slider {
    background-color: {{ theme.ui.accent }};
    border: 1px solid {{ theme.ui.border }};
}

scale trough {
    background-color: {{ theme.colors.bg2 }};
}

/* Calendar */
calendar {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
}

calendar:selected {
    background-color: {{ theme.ui.accent }};
    color: {{ theme.ui.background }};
}

/* Combobox */
combobox {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
    border: 1px solid {{ theme.ui.border }};
}

combobox button {
    background-color: {{ theme.colors.bg2 }};
    border: none;
}

/* Infobar */
.info {
    background-color: {{ theme.ui.info }};
    color: {{ theme.ui.background }};
}

.warning {
    background-color: {{ theme.ui.warning }};
    color: {{ theme.ui.background }};
}

.error {
    background-color: {{ theme.ui.error }};
    color: {{ theme.ui.background }};
}

.success {
    background-color: {{ theme.ui.success }};
    color: {{ theme.ui.background }};
}

/* Dark theme specific adjustments */
{% if theme.variant == "dark" %}
window {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
}

.view {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
}

textview text {
    background-color: {{ theme.ui.background }};
    color: {{ theme.ui.foreground }};
}
{% endif %}
