// Zellij Configuration
// Generated by Universal Theme System
// Theme: {{ theme.name }}

// Use built-in theme if available, otherwise define custom
{% set theme_id = theme.name.lower().replace(' ', '-') %}
{% if theme_id in ['gruvbox-dark', 'gruvbox-light', 'catppuccin-mocha', 'catppuccin-macchiato', 'catppuccin-frappe', 'catppuccin-latte', 'nord', 'dracula', 'tokyo-night', 'tokyo-night-light'] %}
theme "{{ theme_id }}"
{% else %}
// Custom theme definition
themes {
    {{ theme_id }} {
        fg {{ theme.ui.foreground | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
        bg {{ theme.ui.background | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
        red {{ theme.terminal.red | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
        green {{ theme.terminal.green | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
        yellow {{ theme.terminal.yellow | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
        blue {{ theme.terminal.blue | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
        magenta {{ theme.terminal.magenta | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
        orange {{ theme.colors.orange | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
        cyan {{ theme.terminal.cyan | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
        black {{ theme.terminal.black | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
        white {{ theme.terminal.white | replace('#', '') | regex_replace('(..)(..)(..)') | replace('$1', '$1 ') | replace('$2', '$2 ') }}
    }
}

theme "{{ theme_id }}"
{% endif %}

// Key bindings optimized for workflow
keybinds clear-defaults=true {
    normal {
        // Zellij management
        bind "Ctrl s" { SwitchToMode "Scroll"; }
        bind "Alt s" { SwitchToMode "Scroll"; }
        bind "Alt /" { SwitchToMode "Search"; }
        bind "Alt e" { SwitchToMode "EnterSearch"; SearchInput 0; }

        // Pane management
        bind "Alt h" { MoveFocus "Left"; }
        bind "Alt j" { MoveFocus "Down"; }
        bind "Alt k" { MoveFocus "Up"; }
        bind "Alt l" { MoveFocus "Right"; }
        bind "Alt n" { NewPane; }
        bind "Alt p" { NewPane; }
        bind "Alt x" { CloseFocus; }
        bind "Alt v" { NewPane "Right"; }
        bind "Alt -" { NewPane "Down"; }
        bind "Alt f" { ToggleFocusFullscreen; }
        bind "Alt q" { ToggleFocusFullscreen; }

        // Tab management
        bind "Alt t" { NewTab; }
        bind "Alt w" { CloseTab; }
        bind "Alt 1" { GoToTab 1; }
        bind "Alt 2" { GoToTab 2; }
        bind "Alt 3" { GoToTab 3; }
        bind "Alt 4" { GoToTab 4; }
        bind "Alt 5" { GoToTab 5; }
        bind "Alt 6" { GoToTab 6; }
        bind "Alt 7" { GoToTab 7; }
        bind "Alt 8" { GoToTab 8; }
        bind "Alt 9" { GoToTab 9; }
        bind "Alt g" { NewTab; }

        // Copy mode
        bind "Alt y" { Copy; }
    }

    scroll {
        bind "Ctrl s" { SwitchToMode "Normal"; }
        bind "q" { SwitchToMode "Normal"; }
        bind "Esc" { SwitchToMode "Normal"; }
        bind "Alt s" { SwitchToMode "Normal"; }

        // Vim-style navigation
        bind "j" { ScrollDown; }
        bind "k" { ScrollUp; }
        bind "h" { PageScrollUp; }
        bind "l" { PageScrollDown; }
        bind "d" { HalfPageScrollDown; }
        bind "u" { HalfPageScrollUp; }
        bind "g" { ScrollToTop; }
        bind "G" { ScrollToBottom; }

        // Text selection
        bind "v" { SwitchToMode "EnterSearch"; SearchInput 0; }
        bind "V" { SwitchToMode "EnterSearch"; SearchInput 0; }
        bind "Ctrl v" { SwitchToMode "EnterSearch"; SearchInput 0; }
        bind "y" { Copy; }
        bind "/" { SwitchToMode "EnterSearch"; SearchInput 0; }
        bind "?" { SwitchToMode "EnterSearch"; SearchInput 0; }

        // Word navigation
        bind "w" { SwitchToMode "EnterSearch"; SearchInput 0; }
        bind "b" { SwitchToMode "EnterSearch"; SearchInput 0; }
        bind "0" { SwitchToMode "EnterSearch"; SearchInput 0; }
        bind "$" { SwitchToMode "EnterSearch"; SearchInput 0; }
    }

    search {
        bind "Ctrl s" { SwitchToMode "Normal"; }
        bind "q" { SwitchToMode "Normal"; }
        bind "Esc" { SwitchToMode "Normal"; }
        bind "Alt /" { SwitchToMode "Normal"; }
        bind "Enter" { SwitchToMode "Normal"; }
        bind "n" { Search "down"; }
        bind "N" { Search "up"; }
        bind "p" { Search "up"; }
    }

    entersearch {
        bind "Ctrl c" { SwitchToMode "Scroll"; }
        bind "Esc" { SwitchToMode "Scroll"; }
        bind "Enter" { SwitchToMode "Search"; }
    }

    session {
        bind "Ctrl s" { SwitchToMode "Normal"; }
        bind "q" { SwitchToMode "Normal"; }
        bind "Esc" { SwitchToMode "Normal"; }
        bind "d" { Detach; }
    }

    tab {
        bind "Ctrl s" { SwitchToMode "Normal"; }
        bind "q" { SwitchToMode "Normal"; }
        bind "Esc" { SwitchToMode "Normal"; }
        bind "r" { SwitchToMode "RenameTab"; TabNameInput 0; }
        bind "h" { GoToPreviousTab; }
        bind "l" { GoToNextTab; }
        bind "n" { NewTab; SwitchToMode "Normal"; }
        bind "x" { CloseTab; SwitchToMode "Normal"; }
        bind "s" { ToggleActiveSyncTab; SwitchToMode "Normal"; }
        bind "1" { GoToTab 1; SwitchToMode "Normal"; }
        bind "2" { GoToTab 2; SwitchToMode "Normal"; }
        bind "3" { GoToTab 3; SwitchToMode "Normal"; }
        bind "4" { GoToTab 4; SwitchToMode "Normal"; }
        bind "5" { GoToTab 5; SwitchToMode "Normal"; }
        bind "6" { GoToTab 6; SwitchToMode "Normal"; }
        bind "7" { GoToTab 7; SwitchToMode "Normal"; }
        bind "8" { GoToTab 8; SwitchToMode "Normal"; }
        bind "9" { GoToTab 9; SwitchToMode "Normal"; }
        bind "Tab" { ToggleTab; }
    }

    pane {
        bind "Ctrl s" { SwitchToMode "Normal"; }
        bind "q" { SwitchToMode "Normal"; }
        bind "Esc" { SwitchToMode "Normal"; }
        bind "h" { MoveFocus "Left"; }
        bind "l" { MoveFocus "Right"; }
        bind "j" { MoveFocus "Down"; }
        bind "k" { MoveFocus "Up"; }
        bind "p" { SwitchFocus; }
        bind "n" { NewPane; SwitchToMode "Normal"; }
        bind "d" { NewPane "Down"; SwitchToMode "Normal"; }
        bind "r" { NewPane "Right"; SwitchToMode "Normal"; }
        bind "x" { CloseFocus; SwitchToMode "Normal"; }
        bind "f" { ToggleFocusFullscreen; SwitchToMode "Normal"; }
        bind "z" { TogglePaneFrames; SwitchToMode "Normal"; }
        bind "w" { ToggleFloatingPanes; SwitchToMode "Normal"; }
        bind "e" { TogglePaneEmbedOrFloating; SwitchToMode "Normal"; }
        bind "c" { SwitchToMode "RenamePane"; PaneNameInput 0;}
    }

    move {
        bind "Ctrl s" { SwitchToMode "Normal"; }
        bind "q" { SwitchToMode "Normal"; }
        bind "Esc" { SwitchToMode "Normal"; }
        bind "n" { SwitchToMode "Normal"; }
        bind "h" { MovePane "Left"; }
        bind "j" { MovePane "Down"; }
        bind "k" { MovePane "Up"; }
        bind "l" { MovePane "Right"; }
    }

    resize {
        bind "Ctrl s" { SwitchToMode "Normal"; }
        bind "q" { SwitchToMode "Normal"; }
        bind "Esc" { SwitchToMode "Normal"; }
        bind "r" { SwitchToMode "Normal"; }
        bind "h" { Resize "Increase Left"; }
        bind "j" { Resize "Increase Down"; }
        bind "k" { Resize "Increase Up"; }
        bind "l" { Resize "Increase Right"; }
        bind "H" { Resize "Decrease Left"; }
        bind "J" { Resize "Decrease Down"; }
        bind "K" { Resize "Decrease Up"; }
        bind "L" { Resize "Decrease Right"; }
        bind "=" { Resize "Increase"; }
        bind "-" { Resize "Decrease"; }
    }

    renametab {
        bind "Ctrl c" { SwitchToMode "Normal"; }
        bind "Esc" { UndoRenameTab; SwitchToMode "Tab"; }
    }

    renamepane {
        bind "Ctrl c" { SwitchToMode "Normal"; }
        bind "Esc" { UndoRenamePane; SwitchToMode "Pane"; }
    }
}

// Plugin configuration
plugins {
    tab-bar { path "tab-bar"; }
    status-bar { path "status-bar"; }
    strider { path "strider"; }
    compact-bar { path "compact-bar"; }
}

// UI configuration
ui {
    pane_frames {
        rounded_corners true
        hide_session_name false
    }
}

// Copy configuration
copy_command "wl-copy"
copy_clipboard "primary"

// Session configuration
default_shell "zsh"
default_cwd "~"

// Mouse configuration
mouse_mode true
scroll_buffer_size 10000

// Performance
simplified_ui true
default_mode "normal"
mirror_session false
