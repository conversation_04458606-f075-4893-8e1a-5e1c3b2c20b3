# Foot Terminal Configuration
# Generated by Universal Theme System
# Theme: {{ theme.name }}

[main]
term=xterm-256color
login-shell=no
app-id=foot
title=foot
locked-title=no

[bell]
urgent=no
notify=no
visual=no
command-focused=no

[scrollback]
lines=1000
multiplier=3.0
indicator-position=relative
indicator-format=""

[url]
launch=xdg-open ${url}
label-letters=sadfjklewcmpgh
osc8-underline=url-mode
protocols=http, https, ftp, ftps, file, gemini, gopher
uri-characters=abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_.,~:;/?#@!$&%*+="'()[]

[cursor]
style=block
color={{ theme.ui.cursor[1:] }} {{ theme.ui.background[1:] }}
blink=no
beam-thickness=1.5
underline-thickness=<font-size>/16

[mouse]
hide-when-typing=no
alternate-scroll-mode=yes

[colors]
alpha=1.0
background={{ theme.ui.background[1:] }}
foreground={{ theme.ui.foreground[1:] }}

## Normal/regular colors (color palette 0-7)
regular0={{ theme.terminal.black[1:] }}
regular1={{ theme.terminal.red[1:] }}
regular2={{ theme.terminal.green[1:] }}
regular3={{ theme.terminal.yellow[1:] }}
regular4={{ theme.terminal.blue[1:] }}
regular5={{ theme.terminal.magenta[1:] }}
regular6={{ theme.terminal.cyan[1:] }}
regular7={{ theme.terminal.white[1:] }}

## Bright colors (color palette 8-15)
bright0={{ theme.terminal.bright_black[1:] }}
bright1={{ theme.terminal.bright_red[1:] }}
bright2={{ theme.terminal.bright_green[1:] }}
bright3={{ theme.terminal.bright_yellow[1:] }}
bright4={{ theme.terminal.bright_blue[1:] }}
bright5={{ theme.terminal.bright_magenta[1:] }}
bright6={{ theme.terminal.bright_cyan[1:] }}
bright7={{ theme.terminal.bright_white[1:] }}

## Selection colors
selection-foreground={{ theme.ui.foreground[1:] }}
selection-background={{ theme.ui.selection[1:] }}

## Search colors
search-box-no-match={{ theme.ui.background[1:] }} {{ theme.ui.error[1:] }}
search-box-match={{ theme.ui.background[1:] }} {{ theme.ui.success[1:] }}

## URL colors
jump-labels={{ theme.ui.background[1:] }} {{ theme.ui.warning[1:] }}
urls={{ theme.ui.accent[1:] }}

[csd]
preferred=server
size=26
font=<primary font>
color={{ theme.ui.foreground[1:] }}
hide-when-maximized=no
double-click-to-maximize=yes
border-width=0
border-color={{ theme.ui.border[1:] }}
button-width=26
button-color={{ theme.ui.foreground[1:] }}
button-minimize-color={{ theme.ui.warning[1:] }}
button-maximize-color={{ theme.ui.success[1:] }}
button-close-color={{ theme.ui.error[1:] }}

[key-bindings]
scrollback-up-page=Shift+Page_Up
scrollback-up-half-page=none
scrollback-up-line=none
scrollback-down-page=Shift+Page_Down
scrollback-down-half-page=none
scrollback-down-line=none
clipboard-copy=Control+Shift+c XF86Copy
clipboard-paste=Control+Shift+v XF86Paste
primary-paste=Shift+Insert
search-start=Control+Shift+r
font-increase=Control+plus Control+equal Control+KP_Add
font-decrease=Control+minus Control+KP_Subtract
font-reset=Control+0 Control+KP_0
spawn-terminal=Control+Shift+n
minimize=none
maximize=none
fullscreen=F11
pipe-visible=[sh -c "xurls | fuzzel | xargs -r firefox"] none
pipe-scrollback=[sh -c "xurls | fuzzel | xargs -r firefox"] none
pipe-selected=[xargs -r firefox] none
show-urls-launch=Control+Shift+u
show-urls-copy=none
show-urls-persistent=none
prompt-prev=Control+Shift+z
prompt-next=Control+Shift+x
unicode-input=Control+Shift+u
noop=none

[search-bindings]
cancel=Control+g Control+c Escape
commit=Return
find-prev=Control+r
find-next=Control+s
cursor-left=Left Control+b
cursor-left-word=Control+Left Mod1+b
cursor-right=Right Control+f
cursor-right-word=Control+Right Mod1+f
cursor-home=Home Control+a
cursor-end=End Control+e
delete-prev=BackSpace
delete-prev-word=Mod1+BackSpace Control+BackSpace
delete-next=Delete
delete-next-word=Mod1+d Control+Delete
extend-to-word-boundary=Control+w
extend-to-next-whitespace=Control+Shift+w
clipboard-paste=Control+v Control+Shift+v Control+y XF86Paste
primary-paste=Shift+Insert
unicode-input=none

[url-bindings]
cancel=Control+g Control+c Control+d Escape
toggle-url-visible=t

[text-bindings]
\x03=Mod4+c  # Map Super+c to Ctrl+c

[mouse-bindings]
selection-override-modifiers=Shift
primary-paste=BTN_MIDDLE
select-begin=BTN_LEFT
select-begin-block=Control+BTN_LEFT
select-extend=BTN_RIGHT
select-extend-character-wise=Control+BTN_RIGHT
select-word=BTN_LEFT-2
select-word-whitespace=Control+BTN_LEFT-2
select-row=BTN_LEFT-3
