#!/usr/bin/env python3
"""
Fast Theme Switcher - Optimized for speed
No dependencies, direct file manipulation, instant application
"""

import os
import sys
import json
import signal
import subprocess
from pathlib import Path

# Fast theme definitions (embedded for speed)
THEMES = {
    # Classic themes
    "gruvbox-dark": {
        "bg": "#282828", "fg": "#ebdbb2",
        "red": "#cc241d", "green": "#98971a", "yellow": "#d79921",
        "blue": "#d65d0e", "purple": "#b16286", "cyan": "#689d6a"
    },
    "gruvbox-light": {
        "bg": "#fbf1c7", "fg": "#3c3836",
        "red": "#cc241d", "green": "#98971a", "yellow": "#d79921",
        "blue": "#d65d0e", "purple": "#b16286", "cyan": "#689d6a"
    },
    "solarized-dark": {
        "bg": "#002b36", "fg": "#839496",
        "red": "#dc322f", "green": "#859900", "yellow": "#b58900",
        "blue": "#268bd2", "purple": "#d33682", "cyan": "#2aa198"
    },
    "solarized-light": {
        "bg": "#fdf6e3", "fg": "#657b83",
        "red": "#dc322f", "green": "#859900", "yellow": "#b58900",
        "blue": "#268bd2", "purple": "#d33682", "cyan": "#2aa198"
    },
    "monokai": {
        "bg": "#272822", "fg": "#f8f8f2",
        "red": "#f92672", "green": "#a6e22e", "yellow": "#f4bf75",
        "blue": "#66d9ef", "purple": "#ae81ff", "cyan": "#a1efe4"
    },

    # Catppuccin family
    "catppuccin-mocha": {
        "bg": "#1e1e2e", "fg": "#cdd6f4",
        "red": "#f38ba8", "green": "#a6e3a1", "yellow": "#f9e2af",
        "blue": "#89b4fa", "purple": "#cba6f7", "cyan": "#94e2d5"
    },
    "catppuccin-macchiato": {
        "bg": "#24273a", "fg": "#cad3f5",
        "red": "#ed8796", "green": "#a6da95", "yellow": "#eed49f",
        "blue": "#8aadf4", "purple": "#c6a0f6", "cyan": "#91d7e3"
    },
    "catppuccin-frappe": {
        "bg": "#303446", "fg": "#c6d0f5",
        "red": "#e78284", "green": "#a6d189", "yellow": "#e5c890",
        "blue": "#8caaee", "purple": "#ca9ee6", "cyan": "#81c8be"
    },
    "catppuccin-latte": {
        "bg": "#eff1f5", "fg": "#4c4f69",
        "red": "#d20f39", "green": "#40a02b", "yellow": "#df8e1d",
        "blue": "#1e66f5", "purple": "#8839ef", "cyan": "#179299"
    },

    # Tokyo Night family
    "tokyo-night": {
        "bg": "#1a1b26", "fg": "#c0caf5",
        "red": "#f7768e", "green": "#9ece6a", "yellow": "#e0af68",
        "blue": "#7aa2f7", "purple": "#bb9af7", "cyan": "#7dcfff"
    },
    "tokyo-night-light": {
        "bg": "#d5d6db", "fg": "#565a6e",
        "red": "#8c4351", "green": "#33635c", "yellow": "#8f5e15",
        "blue": "#34548a", "purple": "#5a4a78", "cyan": "#0f4b6e"
    },
    "tokyo-night-storm": {
        "bg": "#24283b", "fg": "#c0caf5",
        "red": "#f7768e", "green": "#9ece6a", "yellow": "#e0af68",
        "blue": "#7aa2f7", "purple": "#bb9af7", "cyan": "#7dcfff"
    },

    # Dracula family
    "dracula": {
        "bg": "#282a36", "fg": "#E0E1DF",
        "red": "#ff5555", "green": "#50fa7b", "yellow": "#f1fa8c",
        "blue": "#bd93f9", "purple": "#ff79c6", "cyan": "#8be9fd"
    },
    "dracula-soft": {
        "bg": "#21222c", "fg": "#E0E1DF",
        "red": "#ff6e6e", "green": "#69ff94", "yellow": "#ffffa5",
        "blue": "#d6acff", "purple": "#ff92df", "cyan": "#a4ffff"
    },

    # Nord family
    "nord": {
        "bg": "#2e3440", "fg": "#d8dee9",
        "red": "#bf616a", "green": "#a3be8c", "yellow": "#ebcb8b",
        "blue": "#81a1c1", "purple": "#b48ead", "cyan": "#88c0d0"
    },
    "nord-light": {
        "bg": "#eceff4", "fg": "#2e3440",
        "red": "#bf616a", "green": "#a3be8c", "yellow": "#ebcb8b",
        "blue": "#5e81ac", "purple": "#b48ead", "cyan": "#88c0d0"
    },

    # One Dark family
    "one-dark": {
        "bg": "#282c34", "fg": "#abb2bf",
        "red": "#e06c75", "green": "#98c379", "yellow": "#e5c07b",
        "blue": "#61afef", "purple": "#c678dd", "cyan": "#56b6c2"
    },
    "one-light": {
        "bg": "#E0E1DF", "fg": "#383a42",
        "red": "#e45649", "green": "#50a14f", "yellow": "#c18401",
        "blue": "#4078f2", "purple": "#a626a4", "cyan": "#0184bc"
    },

    # Material family
    "material-dark": {
        "bg": "#212121", "fg": "#E0E1DF",
        "red": "#f07178", "green": "#c3e88d", "yellow": "#ffcb6b",
        "blue": "#82aaff", "purple": "#c792ea", "cyan": "#89ddff"
    },
    "material-light": {
        "bg": "#E0E1DF", "fg": "#90a4ae",
        "red": "#e53935", "green": "#91b859", "yellow": "#ffb62c",
        "blue": "#6182b8", "purple": "#7c4dff", "cyan": "#39adb5"
    },
    "material-ocean": {
        "bg": "#0f111a", "fg": "#8f93a2",
        "red": "#ff5370", "green": "#c3e88d", "yellow": "#ffcb6b",
        "blue": "#82aaff", "purple": "#c792ea", "cyan": "#89ddff"
    },

    # GitHub themes
    "github-dark": {
        "bg": "#0d1117", "fg": "#c9d1d9",
        "red": "#ff7b72", "green": "#7ee787", "yellow": "#ffa657",
        "blue": "#79c0ff", "purple": "#d2a8ff", "cyan": "#a5f3fc"
    },
    "github-light": {
        "bg": "#E0E1DF", "fg": "#24292f",
        "red": "#cf222e", "green": "#116329", "yellow": "#4d2d00",
        "blue": "#0969da", "purple": "#8250df", "cyan": "#1b7c83"
    },

    # Ayu family
    "ayu-dark": {
        "bg": "#0a0e14", "fg": "#b3b1ad",
        "red": "#f07178", "green": "#bae67e", "yellow": "#ffd580",
        "blue": "#73d0ff", "purple": "#d4bfff", "cyan": "#95e6cb"
    },
    "ayu-light": {
        "bg": "#E0E1DF", "fg": "#5c6166",
        "red": "#f51818", "green": "#86b300", "yellow": "#f2ae49",
        "blue": "#399ee6", "purple": "#a37acc", "cyan": "#4cbf99"
    },
    "ayu-mirage": {
        "bg": "#1f2430", "fg": "#cbccc6",
        "red": "#f28779", "green": "#bae67e", "yellow": "#ffd580",
        "blue": "#73d0ff", "purple": "#d4bfff", "cyan": "#95e6cb"
    },

    # Everforest family
    "everforest-dark": {
        "bg": "#2d353b", "fg": "#d3c6aa",
        "red": "#e67e80", "green": "#a7c080", "yellow": "#dbbc7f",
        "blue": "#7fbbb3", "purple": "#d699b6", "cyan": "#83c092"
    },
    "everforest-light": {
        "bg": "#E0E1DF", "fg": "#5c6a72",
        "red": "#f85552", "green": "#8da101", "yellow": "#dfa000",
        "blue": "#3a94c5", "purple": "#df69ba", "cyan": "#35a77c"
    },

    # Rose Pine family
    "rose-pine": {
        "bg": "#191724", "fg": "#e0def4",
        "red": "#eb6f92", "green": "#31748f", "yellow": "#f6c177",
        "blue": "#9ccfd8", "purple": "#c4a7e7", "cyan": "#ebbcba"
    },
    "rose-pine-moon": {
        "bg": "#232136", "fg": "#e0def4",
        "red": "#eb6f92", "green": "#3e8fb0", "yellow": "#f6c177",
        "blue": "#9ccfd8", "purple": "#c4a7e7", "cyan": "#ea9a97"
    },
    "rose-pine-dawn": {
        "bg": "#E0E1DF", "fg": "#575279",
        "red": "#b4637a", "green": "#286983", "yellow": "#ea9d34",
        "blue": "#56949f", "purple": "#907aa9", "cyan": "#d7827e"
    },

    # Custom high-contrast themes
    "prismatic-dark": {
        "bg": "#0d0d0d", "fg": "#E0E1DF",
        "red": "#ff6b6b", "green": "#4ecdc4", "yellow": "#e6b800",
        "blue": "#6ba6cd", "purple": "#7c4dff", "cyan": "#00d4aa"
    },
    "prismatic-light": {
        "bg": "#E0E1DF", "fg": "#2d2d2d",
        "red": "#d63031", "green": "#00b894", "yellow": "#b8860b",
        "blue": "#4682b4", "purple": "#5e35b1", "cyan": "#00acc1"
    },
    "steel-dark": {
        "bg": "#1c1c1c", "fg": "#c7c7c7",
        "red": "#e74c3c", "green": "#27ae60", "yellow": "#d68910",
        "blue": "#5dade2", "purple": "#8e44ad", "cyan": "#48c9b0"
    },
    "obsidian": {
        "bg": "#000000", "fg": "#E0E1DF",
        "red": "#ff4757", "green": "#2ed573", "yellow": "#e6b800",
        "blue": "#3742fa", "purple": "#7c4dff", "cyan": "#26d0ce"
    },
    "platinum": {
        "bg": "#E0E1DF", "fg": "#000000",
        "red": "#c0392b", "green": "#27ae60", "yellow": "#b8860b",
        "blue": "#2980b9", "purple": "#6a1b9a", "cyan": "#17a2b8"
    },
    "copper": {
        "bg": "#2c1810", "fg": "#d4af37",
        "red": "#cd853f", "green": "#8fbc8f", "yellow": "#b8860b",
        "blue": "#4682b4", "purple": "#8e44ad", "cyan": "#20b2aa"
    },

    # Additional popular themes
    "kanagawa": {
        "bg": "#1f1f28", "fg": "#dcd7ba",
        "red": "#c34043", "green": "#76946a", "yellow": "#c0a36e",
        "blue": "#7e9cd8", "purple": "#957fb8", "cyan": "#6a9589"
    },
    "nightfox": {
        "bg": "#192330", "fg": "#cdcecf",
        "red": "#c94f6d", "green": "#81b29a", "yellow": "#dbc074",
        "blue": "#719cd6", "purple": "#9d79d6", "cyan": "#63cdcf"
    },
    "carbonfox": {
        "bg": "#161616", "fg": "#f2f4f8",
        "red": "#ee5396", "green": "#25be6a", "yellow": "#08bdba",
        "blue": "#78a9ff", "purple": "#be95ff", "cyan": "#33b1ff"
    },
    "tokyonight-moon": {
        "bg": "#222436", "fg": "#c8d3f5",
        "red": "#ff757f", "green": "#c3e88d", "yellow": "#ffc777",
        "blue": "#82aaff", "purple": "#c099ff", "cyan": "#86e1fc"
    },

    # Additional popular themes
    "chocolate-gruvbox": {
        "bg": "#2b1d0e", "fg": "#d4be98",
        "red": "#ea6962", "green": "#a9b665", "yellow": "#e78a4e",
        "blue": "#d8a657", "purple": "#d3869b", "cyan": "#89b482"
    },
    "monokai-pro": {
        "bg": "#2d2a2e", "fg": "#E0E1DF",
        "red": "#ff6188", "green": "#a9dc76", "yellow": "#ffd866",
        "blue": "#fc9867", "purple": "#ab9df2", "cyan": "#78dce8"
    },
    "palenight": {
        "bg": "#292d3e", "fg": "#a6accd",
        "red": "#f07178", "green": "#c3e88d", "yellow": "#ffcb6b",
        "blue": "#82aaff", "purple": "#c792ea", "cyan": "#89ddff"
    },
    "synthwave": {
        "bg": "#2a2139", "fg": "#e0e0e0",
        "red": "#fe4450", "green": "#72f1b8", "yellow": "#e6b800",
        "blue": "#03edf9", "purple": "#8b5cf6", "cyan": "#09f7a0"
    },
    "horizon": {
        "bg": "#1c1e26", "fg": "#e0e0e0",
        "red": "#e95678", "green": "#29d398", "yellow": "#d68910",
        "blue": "#26bbd9", "purple": "#8e44ad", "cyan": "#59e3e3"
    },
    "shades-of-purple": {
        "bg": "#2d2b55", "fg": "#a599e9",
        "red": "#d90429", "green": "#3ad900", "yellow": "#e6b800",
        "blue": "#6943ff", "purple": "#7c4dff", "cyan": "#00d9ff"
    },
    "cobalt2": {
        "bg": "#193549", "fg": "#E0E1DF",
        "red": "#ff0000", "green": "#38de21", "yellow": "#e6b800",
        "blue": "#1460d2", "purple": "#8e44ad", "cyan": "#00bbbb"
    },
    "night-owl": {
        "bg": "#011627", "fg": "#d6deeb",
        "red": "#ef5350", "green": "#22da6e", "yellow": "#addb67",
        "blue": "#82aaff", "purple": "#c792ea", "cyan": "#21c7a8"
    },
    "winter-is-coming": {
        "bg": "#0e293f", "fg": "#ffffff",
        "red": "#ff6b68", "green": "#5de4c7", "yellow": "#fffac2",
        "blue": "#add7ff", "purple": "#91ddff", "cyan": "#87dfeb"
    },
    "andromeda": {
        "bg": "#262a33", "fg": "#f7f7f7",
        "red": "#f92672", "green": "#a6e22e", "yellow": "#fd971f",
        "blue": "#66d9ef", "purple": "#ae81ff", "cyan": "#a1efe4"
    },
    "atom-one-dark-pro": {
        "bg": "#1e2127", "fg": "#e06c75",
        "red": "#e06c75", "green": "#98c379", "yellow": "#d19a66",
        "blue": "#61afef", "purple": "#c678dd", "cyan": "#56b6c2"
    },
    "darcula": {
        "bg": "#2b2b2b", "fg": "#a9b7c6",
        "red": "#ff6b68", "green": "#a8c023", "yellow": "#ffc66d",
        "blue": "#6897bb", "purple": "#cc7832", "cyan": "#629755"
    }
}

def hex_to_rgb(hex_color):
    """Convert hex to RGB values."""
    hex_color = hex_color.lstrip('#')
    return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

def apply_foot_theme(theme):
    """Apply theme to Foot terminal."""
    foot_config = Path.home() / ".config/foot/foot.ini"
    foot_config.parent.mkdir(parents=True, exist_ok=True)

    config = f"""[main]
term=xterm-256color
font=IosevkaTerm Nerd Font:size=18
pad=8x8

[colors]
background={theme['bg'][1:]}
foreground={theme['fg'][1:]}
regular0={theme['bg'][1:]}
regular1={theme['red'][1:]}
regular2={theme['green'][1:]}
regular3={theme['yellow'][1:]}
regular4={theme['blue'][1:]}
regular5={theme['purple'][1:]}
regular6={theme['cyan'][1:]}
regular7={theme['fg'][1:]}
bright0=666666
bright1={theme['red'][1:]}
bright2={theme['green'][1:]}
bright3={theme['yellow'][1:]}
bright4={theme['blue'][1:]}
bright5={theme['purple'][1:]}
bright6={theme['cyan'][1:]}
bright7={theme['fg'][1:]}

[cursor]
style=block
color={theme['bg'][1:]} {theme['yellow'][1:]}
blink=no

[mouse]
hide-when-typing=yes

[key-bindings]
clipboard-copy=Control+Shift+c
clipboard-paste=Control+Shift+v
font-increase=Control+plus Control+equal
font-decrease=Control+minus
font-reset=Control+0
spawn-terminal=Control+Shift+n
"""

    with open(foot_config, 'w') as f:
        f.write(config)

def apply_zellij_theme(theme_name, theme):
    """Apply theme to Zellij."""
    zellij_config = Path.home() / ".config/zellij/config.kdl"

    # Use built-in themes when possible
    builtin_map = {
        "gruvbox-dark": "gruvbox-dark",
        "gruvbox-light": "gruvbox-light",
        "catppuccin-mocha": "catppuccin-mocha",
        "nord": "nord"
    }

    if theme_name in builtin_map:
        theme_line = f'theme "{builtin_map[theme_name]}"'
    else:
        # Custom theme
        r, g, b = hex_to_rgb(theme['fg'])
        bg_r, bg_g, bg_b = hex_to_rgb(theme['bg'])
        red_r, red_g, red_b = hex_to_rgb(theme['red'])
        green_r, green_g, green_b = hex_to_rgb(theme['green'])
        yellow_r, yellow_g, yellow_b = hex_to_rgb(theme['yellow'])
        blue_r, blue_g, blue_b = hex_to_rgb(theme['blue'])
        purple_r, purple_g, purple_b = hex_to_rgb(theme['purple'])
        cyan_r, cyan_g, cyan_b = hex_to_rgb(theme['cyan'])

        custom_theme = f"""
themes {{
    {theme_name} {{
        fg {r} {g} {b}
        bg {bg_r} {bg_g} {bg_b}
        red {red_r} {red_g} {red_b}
        green {green_r} {green_g} {green_b}
        yellow {yellow_r} {yellow_g} {yellow_b}
        blue {blue_r} {blue_g} {blue_b}
        magenta {purple_r} {purple_g} {purple_b}
        cyan {cyan_r} {cyan_g} {cyan_b}
        black {bg_r} {bg_g} {bg_b}
        white {r} {g} {b}
        orange 255 165 0
    }}
}}

theme "{theme_name}"
"""

        # Read existing config
        if zellij_config.exists():
            with open(zellij_config, 'r') as f:
                content = f.read()
        else:
            content = """keybinds clear-defaults=true {
    normal {
        bind "Alt s" { SwitchToMode "Scroll"; }
        bind "Alt /" { SwitchToMode "Search"; }
        bind "Alt h" { MoveFocus "Left"; }
        bind "Alt j" { MoveFocus "Down"; }
        bind "Alt k" { MoveFocus "Up"; }
        bind "Alt l" { MoveFocus "Right"; }
        bind "Alt n" { NewPane; }
        bind "Alt x" { CloseFocus; }
        bind "Alt t" { NewTab; }
        bind "Alt w" { CloseTab; }
        bind "Alt f" { ToggleFocusFullscreen; }
    }
    scroll {
        bind "q" { SwitchToMode "Normal"; }
        bind "j" { ScrollDown; }
        bind "k" { ScrollUp; }
        bind "/" { SwitchToMode "EnterSearch"; SearchInput 0; }
    }
    search {
        bind "q" { SwitchToMode "Normal"; }
        bind "n" { Search "down"; }
        bind "N" { Search "up"; }
    }
    entersearch {
        bind "Enter" { SwitchToMode "Search"; }
        bind "Esc" { SwitchToMode "Scroll"; }
    }
}
copy_command "wl-copy"
"""

        # Remove existing themes and theme lines
        lines = content.split('\n')
        filtered = []
        in_themes = False
        brace_count = 0

        for line in lines:
            if line.strip().startswith('theme '):
                continue
            if line.strip().startswith('themes {'):
                in_themes = True
                brace_count = 1
                continue
            if in_themes:
                brace_count += line.count('{') - line.count('}')
                if brace_count <= 0:
                    in_themes = False
                continue
            filtered.append(line)

        content = '\n'.join(filtered) + custom_theme

        with open(zellij_config, 'w') as f:
            f.write(content)
        return

    # For built-in themes, just update the theme line
    if zellij_config.exists():
        with open(zellij_config, 'r') as f:
            content = f.read()

        lines = content.split('\n')
        updated = []
        theme_set = False

        for line in lines:
            if line.strip().startswith('theme '):
                if not theme_set:
                    updated.append(theme_line)
                    theme_set = True
            else:
                updated.append(line)

        if not theme_set:
            updated.append(theme_line)

        with open(zellij_config, 'w') as f:
            f.write('\n'.join(updated))

def apply_hyprland_theme(theme):
    """Apply theme colors to Hyprland decoration.conf with proper light theme handling."""
    decoration_path = Path.home() / ".config/hypr/theme/decoration.conf"

    if not decoration_path.exists():
        return

    # Detect if this is a light theme
    is_light_theme = theme['bg'] in ["#E0E1DF", "#ffffff", "#fafafa", "#fbf1c7", "#fdf6e3", "#eff1f5", "#eceff4", "#d5d6db", "#faf4ed"]

    # Get colors from theme with better fallbacks
    primary_color = theme.get("blue", "#83a598")

    if is_light_theme:
        # For light themes, use darker colors for better visibility
        secondary_color = theme.get("purple", "#8e44ad")
        inactive_color = theme.get("fg", "#2e3440")  # Use foreground color for inactive
    else:
        # For dark themes, use normal handling
        secondary_color = theme.get("purple", "#b16286")
        inactive_color = theme.get("bg", "#3c3836")

    # Convert hex to rgb format (remove # and ensure 6 chars)
    def hex_to_rgb(hex_color):
        hex_color = str(hex_color).lstrip('#')
        if len(hex_color) == 6 and all(c in '0123456789abcdefABCDEF' for c in hex_color):
            return hex_color.lower()
        return "83a598"  # fallback

    primary_hex = hex_to_rgb(primary_color)
    secondary_hex = hex_to_rgb(secondary_color)
    inactive_hex = hex_to_rgb(inactive_color)

    # Read current decoration.conf
    with open(decoration_path, 'r') as f:
        content = f.read()

    # Update border colors using regex with better patterns
    import re

    if is_light_theme:
        # For light themes, use solid borders instead of gradients to avoid black borders
        active_pattern = r'(\s*)col\.active_border\s*=\s*.*'
        new_active = f"\\1col.active_border = rgb({primary_hex})"
        content = re.sub(active_pattern, new_active, content)

        # Use a visible inactive border for light themes with good contrast
        inactive_pattern = r'(\s*)col\.inactive_border\s*=\s*.*'
        new_inactive = f"\\1col.inactive_border = rgba({inactive_hex}cc)"  # More visible
        content = re.sub(inactive_pattern, new_inactive, content)
    else:
        # For dark themes, use gradient borders
        active_pattern = r'(\s*)col\.active_border\s*=\s*.*'
        new_active = f"\\1col.active_border = rgba({primary_hex}ff) rgba({secondary_hex}ff) 45deg"
        content = re.sub(active_pattern, new_active, content)

        # Use visible inactive border instead of transparent - better contrast
        inactive_pattern = r'(\s*)col\.inactive_border\s*=\s*.*'
        # Use a dimmed version of the primary color for inactive borders
        new_inactive = f"\\1col.inactive_border = rgba({primary_hex}80)"  # 50% opacity for visibility
        content = re.sub(inactive_pattern, new_inactive, content)

    # Write back
    with open(decoration_path, 'w') as f:
        f.write(content)

def apply_nvim_theme(theme_name, theme):
    """Apply theme to Neovim."""
    nvim_theme_file = Path.home() / ".config/nvim/lua/theme-auto.lua"
    nvim_theme_file.parent.mkdir(parents=True, exist_ok=True)

    # Map theme names to nvim colorschemes
    nvim_themes = {
        "gruvbox-dark": "gruvbox",
        "gruvbox-light": "gruvbox",
        "catppuccin-mocha": "catppuccin-mocha",
        "catppuccin-latte": "catppuccin-latte",
        "catppuccin-frappe": "catppuccin-frappe",
        "catppuccin-macchiato": "catppuccin-macchiato",
        "tokyonight-night": "tokyonight-night",
        "tokyonight-day": "tokyonight-day",
        "nord": "nord",
        "onedark": "onedark",
        "dracula": "dracula",
    }

    nvim_colorscheme = nvim_themes.get(theme_name, "gruvbox")
    background = "dark" if "dark" in theme_name else "light"

    lua_content = f'''-- Auto-generated theme configuration
vim.opt.background = "{background}"

-- Set colorscheme with fallback
local ok, _ = pcall(vim.cmd, "colorscheme {nvim_colorscheme}")
if not ok then
    vim.cmd("colorscheme default")
end
'''

    with open(nvim_theme_file, 'w') as f:
        f.write(lua_content)

def apply_zellij_theme(theme_name, theme):
    """Apply theme to Zellij terminal multiplexer."""
    zellij_config = Path.home() / ".config/zellij/config.kdl"

    # Create directory if it doesn't exist
    zellij_config.parent.mkdir(parents=True, exist_ok=True)

    # Convert hex colors to RGB values for Zellij
    def hex_to_rgb(hex_color):
        hex_color = hex_color.lstrip('#')
        return f"{int(hex_color[0:2], 16)} {int(hex_color[2:4], 16)} {int(hex_color[4:6], 16)}"

    # Check if we should use a built-in theme
    builtin_themes = {
        'gruvbox-dark': 'gruvbox-dark',
        'gruvbox-light': 'gruvbox-light',
        'catppuccin-mocha': 'catppuccin-mocha',
        'nord': 'nord',
        'dracula': 'dracula',
        'tokyo-night': 'tokyo-night'
    }

    if theme_name in builtin_themes:
        # Use built-in theme
        config_content = f'''// Zellij Configuration - Theme: {theme_name}
theme "{builtin_themes[theme_name]}"

// Key bindings optimized for workflow
keybinds clear-defaults=true {{
    normal {{
        // Zellij management
        bind "Ctrl s" {{ SwitchToMode "Scroll"; }}
        bind "Alt s" {{ SwitchToMode "Scroll"; }}
        bind "Alt /" {{ SwitchToMode "Search"; }}

        // Pane management
        bind "Alt h" {{ MoveFocus "Left"; }}
        bind "Alt j" {{ MoveFocus "Down"; }}
        bind "Alt k" {{ MoveFocus "Up"; }}
        bind "Alt l" {{ MoveFocus "Right"; }}
        bind "Alt n" {{ NewPane; }}
        bind "Alt x" {{ CloseFocus; }}
        bind "Alt v" {{ NewPane "Right"; }}
        bind "Alt -" {{ NewPane "Down"; }}
        bind "Alt f" {{ ToggleFocusFullscreen; }}

        // Tab management
        bind "Alt t" {{ NewTab; }}
        bind "Alt w" {{ CloseTab; }}
        bind "Alt 1" {{ GoToTab 1; }}
        bind "Alt 2" {{ GoToTab 2; }}
        bind "Alt 3" {{ GoToTab 3; }}
        bind "Alt 4" {{ GoToTab 4; }}
        bind "Alt 5" {{ GoToTab 5; }}
        bind "Alt 6" {{ GoToTab 6; }}
        bind "Alt 7" {{ GoToTab 7; }}
        bind "Alt 8" {{ GoToTab 8; }}
        bind "Alt 9" {{ GoToTab 9; }}

        // Copy mode and text selection
        bind "Alt y" {{ Copy; }}
        bind "Alt v" {{ SwitchToMode "Scroll"; }}
        bind "Ctrl v" {{ SwitchToMode "Scroll"; }}

        // Quick copy shortcuts
        bind "Alt c" {{ Copy; }}
        bind "Ctrl c" {{ Copy; }}
    }}

    scroll {{
        bind "Ctrl s" {{ SwitchToMode "Normal"; }}
        bind "q" {{ SwitchToMode "Normal"; }}
        bind "Esc" {{ SwitchToMode "Normal"; }}
        bind "Alt s" {{ SwitchToMode "Normal"; }}

        // Vim-style navigation
        bind "j" {{ ScrollDown; }}
        bind "k" {{ ScrollUp; }}
        bind "d" {{ HalfPageScrollDown; }}
        bind "u" {{ HalfPageScrollUp; }}
        bind "g" {{ ScrollToTop; }}
        bind "G" {{ ScrollToBottom; }}
        bind "h" {{ PageScrollUp; }}
        bind "l" {{ PageScrollDown; }}

        // Enhanced text selection and copying
        bind "v" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "V" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "y" {{ Copy; }}
        bind "Y" {{ Copy; }}
        bind "c" {{ Copy; }}
        bind "C" {{ Copy; }}
        bind "/" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "?" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "n" {{ Search "down"; }}
        bind "N" {{ Search "up"; }}

        // Word and line navigation
        bind "w" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "b" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "0" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "$" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}

        // Mouse support for selection
        bind "Space" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "Enter" {{ Copy; }}
    }}

    search {{
        bind "Ctrl s" {{ SwitchToMode "Normal"; }}
        bind "q" {{ SwitchToMode "Normal"; }}
        bind "Esc" {{ SwitchToMode "Normal"; }}
        bind "Enter" {{ SwitchToMode "Normal"; }}
        bind "n" {{ Search "down"; }}
        bind "N" {{ Search "up"; }}
    }}

    entersearch {{
        bind "Ctrl c" {{ SwitchToMode "Scroll"; }}
        bind "Esc" {{ SwitchToMode "Scroll"; }}
        bind "Enter" {{ SwitchToMode "Search"; }}
    }}
}}

// UI configuration
ui {{
    pane_frames {{
        rounded_corners true
        hide_session_name false
    }}
}}

// Copy configuration
copy_command "wl-copy"
copy_clipboard "primary"

// Session configuration
default_shell "zsh"
default_cwd "~"

// Mouse configuration
mouse_mode true
scroll_buffer_size 10000

// Performance
simplified_ui false
default_mode "normal"
mirror_session false

// Status bar configuration for better instructions
status_bar {{
    show_tabs true
    show_session_name true
    show_datetime true
    datetime_format "%H:%M"
    datetime_timezone "local"
}}
'''
    else:
        # Create custom theme
        config_content = f'''// Zellij Configuration - Custom Theme: {theme_name}
themes {{
    {theme_name.replace('-', '_')} {{
        fg {hex_to_rgb(theme['fg'])}
        bg {hex_to_rgb(theme['bg'])}
        red {hex_to_rgb(theme['red'])}
        green {hex_to_rgb(theme['green'])}
        yellow {hex_to_rgb(theme['yellow'])}
        blue {hex_to_rgb(theme['blue'])}
        magenta {hex_to_rgb(theme['purple'])}
        orange {hex_to_rgb(theme['yellow'])}
        cyan {hex_to_rgb(theme['cyan'])}
        black {hex_to_rgb(theme['bg'])}
        white {hex_to_rgb(theme['fg'])}
    }}
}}

theme "{theme_name.replace('-', '_')}"

// Key bindings optimized for workflow
keybinds clear-defaults=true {{
    normal {{
        // Zellij management
        bind "Ctrl s" {{ SwitchToMode "Scroll"; }}
        bind "Alt s" {{ SwitchToMode "Scroll"; }}
        bind "Alt /" {{ SwitchToMode "Search"; }}

        // Pane management
        bind "Alt h" {{ MoveFocus "Left"; }}
        bind "Alt j" {{ MoveFocus "Down"; }}
        bind "Alt k" {{ MoveFocus "Up"; }}
        bind "Alt l" {{ MoveFocus "Right"; }}
        bind "Alt n" {{ NewPane; }}
        bind "Alt x" {{ CloseFocus; }}
        bind "Alt v" {{ NewPane "Right"; }}
        bind "Alt -" {{ NewPane "Down"; }}
        bind "Alt f" {{ ToggleFocusFullscreen; }}

        // Tab management
        bind "Alt t" {{ NewTab; }}
        bind "Alt w" {{ CloseTab; }}
        bind "Alt 1" {{ GoToTab 1; }}
        bind "Alt 2" {{ GoToTab 2; }}
        bind "Alt 3" {{ GoToTab 3; }}
        bind "Alt 4" {{ GoToTab 4; }}
        bind "Alt 5" {{ GoToTab 5; }}
        bind "Alt 6" {{ GoToTab 6; }}
        bind "Alt 7" {{ GoToTab 7; }}
        bind "Alt 8" {{ GoToTab 8; }}
        bind "Alt 9" {{ GoToTab 9; }}

        // Copy mode and text selection
        bind "Alt y" {{ Copy; }}
        bind "Alt v" {{ SwitchToMode "Scroll"; }}
        bind "Ctrl v" {{ SwitchToMode "Scroll"; }}

        // Quick copy shortcuts
        bind "Alt c" {{ Copy; }}
        bind "Ctrl c" {{ Copy; }}
    }}

    scroll {{
        bind "Ctrl s" {{ SwitchToMode "Normal"; }}
        bind "q" {{ SwitchToMode "Normal"; }}
        bind "Esc" {{ SwitchToMode "Normal"; }}
        bind "Alt s" {{ SwitchToMode "Normal"; }}

        // Vim-style navigation
        bind "j" {{ ScrollDown; }}
        bind "k" {{ ScrollUp; }}
        bind "d" {{ HalfPageScrollDown; }}
        bind "u" {{ HalfPageScrollUp; }}
        bind "g" {{ ScrollToTop; }}
        bind "G" {{ ScrollToBottom; }}
        bind "h" {{ PageScrollUp; }}
        bind "l" {{ PageScrollDown; }}

        // Enhanced text selection and copying
        bind "v" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "V" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "y" {{ Copy; }}
        bind "Y" {{ Copy; }}
        bind "c" {{ Copy; }}
        bind "C" {{ Copy; }}
        bind "/" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "?" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "n" {{ Search "down"; }}
        bind "N" {{ Search "up"; }}

        // Word and line navigation
        bind "w" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "b" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "0" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "$" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}

        // Mouse support for selection
        bind "Space" {{ SwitchToMode "EnterSearch"; SearchInput 0; }}
        bind "Enter" {{ Copy; }}
    }}

    search {{
        bind "Ctrl s" {{ SwitchToMode "Normal"; }}
        bind "q" {{ SwitchToMode "Normal"; }}
        bind "Esc" {{ SwitchToMode "Normal"; }}
        bind "Enter" {{ SwitchToMode "Normal"; }}
        bind "n" {{ Search "down"; }}
        bind "N" {{ Search "up"; }}
    }}

    entersearch {{
        bind "Ctrl c" {{ SwitchToMode "Scroll"; }}
        bind "Esc" {{ SwitchToMode "Scroll"; }}
        bind "Enter" {{ SwitchToMode "Search"; }}
    }}
}}

// UI configuration
ui {{
    pane_frames {{
        rounded_corners true
        hide_session_name false
    }}
}}

// Copy configuration
copy_command "wl-copy"
copy_clipboard "primary"

// Session configuration
default_shell "zsh"
default_cwd "~"

// Mouse configuration
mouse_mode true
scroll_buffer_size 10000

// Performance
simplified_ui false
default_mode "normal"
mirror_session false

// Status bar configuration for better instructions
status_bar {{
    show_tabs true
    show_session_name true
    show_datetime true
    datetime_format "%H:%M"
    datetime_timezone "local"
}}
'''

    # Write the config
    with open(zellij_config, 'w') as f:
        f.write(config_content)

def apply_gtk_theme(theme, theme_name):
    """Apply theme to GTK applications with improved contrast."""
    gtk3_css = Path.home() / ".config/gtk-3.0/gtk.css"
    gtk4_css = Path.home() / ".config/gtk-4.0/gtk.css"

    # Create directories if they don't exist
    gtk3_css.parent.mkdir(parents=True, exist_ok=True)
    gtk4_css.parent.mkdir(parents=True, exist_ok=True)

    # Calculate contrast colors for better readability
    is_dark = "dark" in theme_name or theme_name in ["obsidian", "steel-dark", "copper", "kanagawa", "nightfox", "carbonfox"]

    # Enhanced color definitions with calm, paper-like whites
    # Define calm silver/paper white instead of bright white
    paper_white = "#E0E1DF"  # Calm, paper-like white
    soft_silver = "#D8D9D7"  # Softer silver tone

    if is_dark:
        # Dark theme colors
        button_bg = theme['bg']
        button_fg = paper_white  # Calm paper white instead of bright white
        button_hover_bg = theme['blue']
        button_hover_fg = paper_white
        entry_bg = theme['bg']
        entry_fg = paper_white  # Calm white text
        border_color = theme['blue']
        focus_color = theme['blue']
        disabled_fg = "#888888"
        selection_bg = theme['blue']
        selection_fg = paper_white
        window_bg = theme['bg']
        window_fg = paper_white
    else:
        # Light theme - calm contrast with paper whites
        button_bg = paper_white  # Paper white instead of pure white
        button_fg = "#1a1a1a"  # Soft black instead of pure black
        button_hover_bg = theme['blue']
        button_hover_fg = paper_white
        entry_bg = paper_white  # Paper white background
        entry_fg = "#1a1a1a"  # Soft black text
        border_color = theme['blue']
        focus_color = theme['blue']
        disabled_fg = "#666666"
        selection_bg = theme['blue']
        selection_fg = paper_white
        window_bg = paper_white
        window_fg = "#1a1a1a"

    # Generate enhanced GTK CSS content
    css_content = f"""/* GTK Theme - {theme_name.title()} */
/* Generated by Universal Theme System - Enhanced Contrast */

@define-color theme_bg_color {window_bg};
@define-color theme_fg_color {window_fg};
@define-color theme_base_color {entry_bg};
@define-color theme_text_color {entry_fg};
@define-color theme_selected_bg_color {selection_bg};
@define-color theme_selected_fg_color {selection_fg};
@define-color insensitive_bg_color {window_bg};
@define-color insensitive_fg_color {disabled_fg};
@define-color borders {border_color};
@define-color warning_color {theme['yellow']};
@define-color error_color {theme['red']};
@define-color success_color {theme['green']};

/* Base window styling */
window {{
    background-color: {window_bg};
    color: {window_fg};
    font-weight: 400;
}}

/* Improved titlebar */
.titlebar {{
    background: linear-gradient(to bottom, {window_bg}, {window_bg});
    color: {window_fg};
    border-bottom: 2px solid {border_color};
    font-weight: 600;
}}

.titlebar:backdrop {{
    background-color: {window_bg};
    color: {disabled_fg};
}}

/* Enhanced buttons with better contrast */
button {{
    background: linear-gradient(to bottom, {button_bg}, {button_bg});
    color: {button_fg};
    border: 2px solid {border_color};
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.2s ease;
}}

button:hover {{
    background: linear-gradient(to bottom, {button_hover_bg}, {button_hover_bg});
    color: {button_hover_fg};
    border-color: {focus_color};
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}}

button:active {{
    background: {button_hover_bg};
    color: {button_hover_fg};
    border-color: {focus_color};
    box-shadow: inset 0 2px 4px rgba(0,0,0,0.2);
}}

button:disabled {{
    background-color: {theme['bg']};
    color: {disabled_fg};
    border-color: {disabled_fg};
    opacity: 0.6;
}}

/* Improved entry fields */
entry {{
    background-color: {entry_bg};
    color: {entry_fg};
    border: 2px solid {border_color};
    border-radius: 6px;
    padding: 8px 12px;
    font-weight: 400;
}}

entry:focus {{
    border-color: {focus_color};
    box-shadow: 0 0 0 2px {focus_color}33;
    outline: none;
}}

entry:disabled {{
    background-color: {theme['bg']};
    color: {disabled_fg};
    border-color: {disabled_fg};
}}

/* Enhanced selection */
selection {{
    background-color: {theme['blue']};
    color: {button_hover_fg};
}}

/* Text views and content areas */
.view {{
    background-color: {entry_bg};
    color: {entry_fg};
}}

textview text {{
    background-color: {entry_bg};
    color: {entry_fg};
    selection-background-color: {theme['blue']};
    selection-color: {button_hover_fg};
}}

/* Menu styling */
menu {{
    background-color: {theme['bg']};
    color: {theme['fg']};
    border: 1px solid {border_color};
}}

menuitem {{
    padding: 6px 12px;
}}

menuitem:hover {{
    background-color: {theme['blue']};
    color: {button_hover_fg};
}}

/* Scrollbars */
scrollbar {{
    background-color: {theme['bg']};
}}

scrollbar slider {{
    background-color: {border_color};
    border-radius: 10px;
}}

scrollbar slider:hover {{
    background-color: {theme['blue']};
}}

/* Notebook tabs */
notebook tab {{
    background-color: {theme['bg']};
    color: {theme['fg']};
    border: 1px solid {border_color};
    padding: 8px 16px;
}}

notebook tab:checked {{
    background-color: {theme['blue']};
    color: {button_hover_fg};
}}

/* CRITICAL: File manager selection fixes */
.view:selected, row:selected, *:selected {{
    background-color: {selection_bg} !important;
    color: {selection_fg} !important;
}}

/* Specific file manager fixes */
.nautilus-window .view:selected,
.thunar .view:selected,
.pcmanfm .view:selected,
iconview:selected,
treeview:selected,
listview row:selected {{
    background-color: {selection_bg} !important;
    color: {selection_fg} !important;
}}

/* Force override any conflicting styles */
* {{
    -gtk-icon-theme: inherit;
}}

/* Additional contrast improvements */
.sidebar {{
    background-color: {window_bg};
    color: {window_fg};
    border-right: 1px solid {border_color};
}}

toolbar {{
    background-color: {window_bg};
    color: {window_fg};
    border-bottom: 1px solid {border_color};
    padding: 4px;
}}

statusbar {{
    background-color: {window_bg};
    color: {window_fg};
    border-top: 1px solid {border_color};
    padding: 4px 8px;
}}

label {{
    color: {window_fg};
    font-weight: 500;
}}

/* Force text contrast in all contexts */
* {{
    color: {window_fg};
}}

/* Override any theme-specific text that might be hard to read */
.view, textview, textview text {{
    background-color: {entry_bg} !important;
    color: {entry_fg} !important;
}}
"""

    # Write to both GTK3 and GTK4
    with open(gtk3_css, 'w') as f:
        f.write(css_content)
    with open(gtk4_css, 'w') as f:
        f.write(css_content)

    # Reload GTK theme using nwg-look for consistency
    try:
        # Use nwg-look to apply stored settings for consistency
        subprocess.run(['nwg-look', '-a'], check=False)

        # Also set gsettings as fallback
        subprocess.run(['gsettings', 'set', 'org.gnome.desktop.interface', 'gtk-theme', 'Adwaita'], check=False)
        subprocess.run(['gsettings', 'set', 'org.gnome.desktop.interface', 'color-scheme',
                       'prefer-dark' if is_dark else 'prefer-light'], check=False)
    except:
        pass

def apply_p10k_theme(theme, theme_name):
    """Apply theme to Powerlevel10k prompt."""
    p10k_config = Path.home() / ".config/zsh/.p10k.zsh"

    if not p10k_config.exists():
        return

    # Determine if theme is dark
    is_dark = "dark" in theme_name or theme_name in ["obsidian", "steel-dark", "copper", "kanagawa", "nightfox", "carbonfox"]

    # Map theme colors to appropriate terminal color codes for gruvbox
    if "gruvbox-dark" in theme_name:
        dir_bg = "4"      # blue background for directory
        ok_color = "76"   # green for OK prompt
        error_color = "196"  # red for error prompt
        vcs_clean = "2"   # green for clean VCS
        vcs_modified = "3"  # yellow for modified VCS
    elif "gruvbox-light" in theme_name:
        dir_bg = "4"      # blue background for directory
        ok_color = "28"   # dark green for OK prompt
        error_color = "124"  # dark red for error prompt
        vcs_clean = "28"  # dark green for clean VCS
        vcs_modified = "136"  # dark yellow for modified VCS
    else:
        # Default colors for other themes
        dir_bg = "4"
        ok_color = "76"
        error_color = "196"
        vcs_clean = "2"
        vcs_modified = "3"

    # Read current p10k config
    with open(p10k_config, 'r') as f:
        content = f.read()

    # Update key color settings with more specific patterns
    import re

    # Update directory colors
    content = re.sub(r'typeset -g POWERLEVEL9K_DIR_BACKGROUND=\d+',
                     f'typeset -g POWERLEVEL9K_DIR_BACKGROUND={dir_bg}', content)

    # Update prompt char colors to follow theme colors
    # Simple color mapping based on theme
    if "synthwave" in theme_name:
        ok_color = "51"   # Cyan for synthwave
        error_color = "196"  # Red
    elif "rose-pine" in theme_name:
        ok_color = "135"  # Purple for rose-pine
        error_color = "203"  # Pink-red
    elif "gruvbox" in theme_name:
        ok_color = "142" if is_dark else "100"  # Gruvbox green
        error_color = "167" if is_dark else "124"  # Gruvbox red
    elif "catppuccin" in theme_name:
        ok_color = "115"  # Catppuccin green
        error_color = "210"  # Catppuccin red
    elif "tokyo-night" in theme_name:
        ok_color = "73"   # Tokyo night green
        error_color = "203"  # Tokyo night red
    elif "dracula" in theme_name:
        ok_color = "84"   # Dracula green
        error_color = "212"  # Dracula red
    elif "nord" in theme_name:
        ok_color = "108"  # Nord green
        error_color = "174"  # Nord red
    else:
        # Default colors based on theme brightness
        ok_color = "76" if is_dark else "28"    # Bright/dark green
        error_color = "196" if is_dark else "124"  # Bright/dark red

    content = re.sub(r'typeset -g POWERLEVEL9K_PROMPT_CHAR_OK_\{VIINS,VICMD,VIVIS,VIOWR\}_FOREGROUND=\d+',
                     f'typeset -g POWERLEVEL9K_PROMPT_CHAR_OK_{{VIINS,VICMD,VIVIS,VIOWR}}_FOREGROUND={ok_color}', content)
    content = re.sub(r'typeset -g POWERLEVEL9K_PROMPT_CHAR_ERROR_\{VIINS,VICMD,VIVIS,VIOWR\}_FOREGROUND=\d+',
                     f'typeset -g POWERLEVEL9K_PROMPT_CHAR_ERROR_{{VIINS,VICMD,VIVIS,VIOWR}}_FOREGROUND={error_color}', content)

    # Update VCS colors
    content = re.sub(r'typeset -g POWERLEVEL9K_VCS_CLEAN_BACKGROUND=\d+',
                     f'typeset -g POWERLEVEL9K_VCS_CLEAN_BACKGROUND={vcs_clean}', content)
    content = re.sub(r'typeset -g POWERLEVEL9K_VCS_MODIFIED_BACKGROUND=\d+',
                     f'typeset -g POWERLEVEL9K_VCS_MODIFIED_BACKGROUND={vcs_modified}', content)
    content = re.sub(r'typeset -g POWERLEVEL9K_VCS_UNTRACKED_BACKGROUND=\d+',
                     f'typeset -g POWERLEVEL9K_VCS_UNTRACKED_BACKGROUND={vcs_clean}', content)

    # Write back
    with open(p10k_config, 'w') as f:
        f.write(content)

def apply_fzf_theme(theme, theme_name):
    """Apply theme colors to FZF."""
    # Set FZF colors based on theme
    fzf_colors = f"""
export FZF_DEFAULT_OPTS="--color=fg:{theme['fg']},bg:{theme['bg']},hl:{theme['blue']} \\
--color=fg+:{theme['fg']},bg+:{theme['purple']},hl+:{theme['cyan']} \\
--color=info:{theme['yellow']},prompt:{theme['green']},pointer:{theme['red']} \\
--color=marker:{theme['red']},spinner:{theme['yellow']},header:{theme['blue']} \\
--color=border:{theme['purple']},label:{theme['blue']},query:{theme['fg']} \\
--border=rounded --height=60% --layout=reverse --margin=1 --padding=1"
"""

    # Write to FZF config file
    fzf_config = Path.home() / ".config/zsh/fzf-theme.zsh"
    fzf_config.parent.mkdir(parents=True, exist_ok=True)

    with open(fzf_config, 'w') as f:
        f.write(fzf_colors)

def apply_atuin_theme(theme, theme_name):
    """Apply theme colors to Atuin."""
    atuin_config = Path.home() / ".config/atuin/config.toml"

    if not atuin_config.exists():
        return

    try:
        # Read existing config
        with open(atuin_config, 'r') as f:
            content = f.read()

        # Define color mappings
        style_config = f"""
# Theme colors for Atuin
style = "compact"

[colors]
# Main interface colors
foreground = "{theme['fg']}"
background = "{theme['bg']}"
border = "{theme['purple']}"

# Search interface
search_highlight = "{theme['yellow']}"
selected_bg = "{theme['blue']}"
selected_fg = "{theme['bg']}"

# Command highlighting
command = "{theme['green']}"
time = "{theme['cyan']}"
directory = "{theme['blue']}"
"""

        # Remove existing color config if present
        import re
        content = re.sub(r'\[colors\].*?(?=\n\[|\nstyle|\Z)', '', content, flags=re.DOTALL)
        content = re.sub(r'style\s*=.*?\n', '', content)

        # Add new theme config
        content = content.strip() + "\n\n" + style_config

        with open(atuin_config, 'w') as f:
            f.write(content)

    except Exception as e:
        print(f"Warning: Could not update Atuin config: {e}")

def apply_hyprpanel_theme(theme, theme_name):
    """Apply theme colors to Hyprpanel with robust JSON handling."""
    config_path = Path.home() / ".config/hyprpanel/config.json"

    if not config_path.exists():
        return

    try:
        import json
        import re

        # Read config file
        with open(config_path, 'r') as f:
            content = f.read().strip()

        # Robust JSON cleanup and validation
        if not content:
            return

        # Multiple cleanup passes for robust JSON fixing
        # 1. Remove trailing commas before closing braces/brackets
        content = re.sub(r',(\s*[}\]])', r'\1', content)

        # 2. Fix common JSON issues
        content = re.sub(r'([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:', r'\1"\2":', content)  # Quote unquoted keys
        content = re.sub(r':\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*([,}])', r': "\1"\2', content)  # Quote unquoted string values

        # 3. Ensure proper JSON structure
        content = content.strip()
        if not content.startswith('{'):
            content = '{' + content
        if not content.endswith('}'):
            content = content.rstrip(',') + '}'

        # 4. Parse JSON with multiple fallback attempts
        config = None

        # First attempt: Direct parsing
        try:
            config = json.loads(content)
        except json.JSONDecodeError:
            pass

        # Second attempt: Try to fix and parse again
        if config is None:
            try:
                # More aggressive cleanup
                content = re.sub(r'([{,]\s*)([^":\s]+)\s*:', r'\1"\2":', content)
                content = re.sub(r':\s*([^",}\s]+)\s*([,}])', r': "\1"\2', content)
                config = json.loads(content)
            except json.JSONDecodeError:
                pass

        # Third attempt: Create minimal config if all else fails
        if config is None:
            print(f"Warning: Could not parse hyprpanel JSON, creating minimal config")
            config = {"theme": {}}

        # Ensure config is a dictionary
        if not isinstance(config, dict):
            print(f"Warning: Hyprpanel config is not a valid object, creating new one")
            config = {"theme": {}}

        # Helper function to safely set config values
        def safe_set(key, value):
            try:
                config[key] = value
            except:
                pass

        # Determine if theme is dark based on background brightness
        def is_dark_theme(bg_color):
            # Remove # if present
            bg_color = bg_color.lstrip('#')
            # Convert to RGB
            try:
                r = int(bg_color[0:2], 16)
                g = int(bg_color[2:4], 16)
                b = int(bg_color[4:6], 16)
                # Calculate luminance
                luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
                return luminance < 0.5
            except:
                # Fallback to name-based detection
                return "dark" in theme_name or theme_name in ["obsidian", "steel-dark", "copper", "kanagawa", "nightfox", "carbonfox", "dracula"]

        # Calculate proper text color with better contrast
        def get_contrast_color(bg_color, fg_color):
            # Remove # if present
            bg_color = bg_color.lstrip('#')
            fg_color = fg_color.lstrip('#')

            try:
                # Convert background to RGB
                bg_r = int(bg_color[0:2], 16)
                bg_g = int(bg_color[2:4], 16)
                bg_b = int(bg_color[4:6], 16)
                bg_luminance = (0.299 * bg_r + 0.587 * bg_g + 0.114 * bg_b) / 255

                # Convert foreground to RGB
                fg_r = int(fg_color[0:2], 16)
                fg_g = int(fg_color[2:4], 16)
                fg_b = int(fg_color[4:6], 16)
                fg_luminance = (0.299 * fg_r + 0.587 * fg_g + 0.114 * fg_b) / 255

                # Calculate contrast ratio
                contrast = (max(bg_luminance, fg_luminance) + 0.05) / (min(bg_luminance, fg_luminance) + 0.05)

                # If contrast is too low, use high contrast color
                if contrast < 3.0:  # WCAG AA minimum
                    if bg_luminance < 0.5:
                        return "#FFFFFF"  # White for dark backgrounds
                    else:
                        return "#000000"  # Black for light backgrounds
                else:
                    return "#" + fg_color  # Use original if contrast is good
            except:
                return "#" + fg_color  # Fallback to original

        is_dark = is_dark_theme(theme['bg'])

        # Use high-contrast text color for better readability
        text_color = get_contrast_color(theme['bg'], theme['fg'])

        # Disable matugen to use our custom colors
        safe_set("theme.matugen", False)
        safe_set("theme.matugen_settings.mode", "dark" if is_dark else "light")

        # COMPREHENSIVE THEMING SYSTEM - Apply to ALL elements systematically

        # Define color roles for consistency
        primary_color = theme['blue']
        secondary_color = theme['purple']
        accent_color = theme['cyan']
        warning_color = theme['yellow']
        error_color = theme['red']
        success_color = theme['green']
        background_color = theme['bg']

        # UNIVERSAL BASE THEMING - applies to everything
        universal_keys = [
            "theme.bar.background",
            "theme.bar.text",
            "theme.bar.border.color",
            "theme.bar.buttons.background",
            "theme.bar.buttons.text",
            "theme.bar.buttons.icon_background",
            "theme.bar.buttons.borderColor",
            "theme.bar.buttons.hover"
        ]

        universal_values = [
            background_color,
            text_color,
            primary_color,
            background_color,
            text_color,
            background_color,
            primary_color,
            secondary_color
        ]

        for key, value in zip(universal_keys, universal_values):
            safe_set(key, value)

        # WORKSPACE THEMING - comprehensive coverage
        workspace_keys = [
            "theme.bar.buttons.workspaces.background",
            "theme.bar.buttons.workspaces.text",
            "theme.bar.buttons.workspaces.active",
            "theme.bar.buttons.workspaces.occupied",
            "theme.bar.buttons.workspaces.available",
            "theme.bar.buttons.workspaces.hover",
            "theme.bar.buttons.workspaces.border",
            "theme.bar.buttons.workspaces.numbered_active_highlighted_text_color",
            "theme.bar.buttons.workspaces.numbered_active_underline_color"
        ]

        workspace_values = [
            background_color,
            text_color,
            primary_color,
            error_color,
            accent_color,
            secondary_color,
            primary_color,
            text_color,
            primary_color
        ]

        for key, value in zip(workspace_keys, workspace_values):
            safe_set(key, value)

        # ALL BUTTON THEMING - systematic and comprehensive
        all_buttons = ['volume', 'network', 'battery', 'clock', 'dashboard', 'notifications',
                      'media', 'bluetooth', 'windowtitle', 'systray', 'power', 'settings']

        # Button icon color mapping - consistent roles
        button_icon_colors = {
            'volume': warning_color,
            'network': secondary_color,
            'battery': success_color,
            'clock': accent_color,
            'dashboard': warning_color,
            'notifications': warning_color,
            'media': primary_color,
            'bluetooth': primary_color,
            'windowtitle': accent_color,
            'systray': secondary_color,
            'power': error_color,
            'settings': primary_color
        }

        # Apply systematic theming to ALL buttons
        for button in all_buttons:
            button_keys = [
                f"theme.bar.buttons.{button}.background",
                f"theme.bar.buttons.{button}.icon_background",
                f"theme.bar.buttons.{button}.text",
                f"theme.bar.buttons.{button}.border",
                f"theme.bar.buttons.{button}.icon"
            ]

            button_values = [
                background_color,
                background_color,
                text_color,
                primary_color,
                button_icon_colors.get(button, primary_color)
            ]

            for key, value in zip(button_keys, button_values):
                safe_set(key, value)

        # ALL MODULE THEMING - systematic coverage
        all_modules = ['cpu', 'ram', 'storage', 'weather', 'updates', 'kbLayout', 'netstat', 'power',
                      'hyprsunset', 'hypridle', 'cava', 'submap']

        # Module icon color mapping - semantic roles
        module_icon_colors = {
            'cpu': error_color,
            'ram': error_color,
            'storage': primary_color,
            'weather': warning_color,
            'updates': secondary_color,
            'kbLayout': primary_color,
            'netstat': success_color,
            'power': error_color,
            'hyprsunset': warning_color,
            'hypridle': primary_color,
            'cava': accent_color,
            'submap': success_color
        }

        # Apply systematic theming to ALL modules
        for module in all_modules:
            module_keys = [
                f"theme.bar.buttons.modules.{module}.background",
                f"theme.bar.buttons.modules.{module}.icon_background",
                f"theme.bar.buttons.modules.{module}.text",
                f"theme.bar.buttons.modules.{module}.border",
                f"theme.bar.buttons.modules.{module}.icon"
            ]

            module_values = [
                background_color,
                background_color,
                text_color,
                primary_color,
                module_icon_colors.get(module, primary_color)
            ]

            for key, value in zip(module_keys, module_values):
                safe_set(key, value)

        # ALL MENU THEMING - systematic and comprehensive
        menu_base_keys = [
            "theme.bar.menus.background",
            "theme.bar.menus.text",
            "theme.bar.menus.border.color",
            "theme.bar.menus.cards",
            "theme.bar.menus.label",
            "theme.bar.menus.buttons.default",
            "theme.bar.menus.buttons.active",
            "theme.bar.menus.buttons.text",
            "theme.bar.menus.buttons.disabled",
            "theme.bar.menus.dimtext",
            "theme.bar.menus.feinttext",
            "theme.bar.menus.icons.active",
            "theme.bar.menus.icons.passive",
            "theme.bar.menus.listitems.active",
            "theme.bar.menus.listitems.passive",
            "theme.bar.menus.iconbuttons.active",
            "theme.bar.menus.iconbuttons.passive"
        ]

        menu_base_values = [
            background_color,
            text_color,
            secondary_color,
            background_color,
            primary_color,
            primary_color,
            accent_color,
            text_color,
            secondary_color,
            secondary_color,
            secondary_color,
            primary_color,
            secondary_color,
            primary_color,
            text_color,
            primary_color,
            text_color
        ]

        for key, value in zip(menu_base_keys, menu_base_values):
            safe_set(key, value)

        # ALL NOTIFICATION THEMING - systematic
        notification_keys = [
            "theme.notification.background",
            "theme.notification.text",
            "theme.notification.border",
            "theme.notification.label",
            "theme.notification.labelicon",
            "theme.notification.time",
            "theme.notification.actions.background",
            "theme.notification.actions.text",
            "theme.notification.close_button.background",
            "theme.notification.close_button.label"
        ]

        notification_values = [
            background_color,
            text_color,
            secondary_color,
            primary_color,
            primary_color,
            secondary_color,
            primary_color,
            background_color,
            primary_color,
            background_color
        ]

        for key, value in zip(notification_keys, notification_values):
            safe_set(key, value)

        # ALL OSD THEMING - systematic
        osd_keys = [
            "theme.osd.bar_color",
            "theme.osd.bar_container",
            "theme.osd.bar_empty_color",
            "theme.osd.bar_overflow_color",
            "theme.osd.icon_container",
            "theme.osd.icon",
            "theme.osd.label"
        ]

        osd_values = [
            primary_color,
            background_color,
            secondary_color,
            error_color,
            primary_color,
            background_color,
            primary_color
        ]

        for key, value in zip(osd_keys, osd_values):
            safe_set(key, value)

        # ALL SPECIFIC MENU THEMING - systematic coverage
        all_menu_types = ['notifications', 'volume', 'network', 'bluetooth', 'battery', 'clock',
                         'dashboard', 'media', 'power', 'systray']

        for menu_type in all_menu_types:
            specific_menu_keys = [
                f"theme.bar.menus.menu.{menu_type}.background.color",
                f"theme.bar.menus.menu.{menu_type}.border.color",
                f"theme.bar.menus.menu.{menu_type}.card.color",
                f"theme.bar.menus.menu.{menu_type}.text",
                f"theme.bar.menus.menu.{menu_type}.label.color"
            ]

            specific_menu_values = [
                background_color,
                secondary_color,
                background_color,
                text_color,
                primary_color
            ]

            for key, value in zip(specific_menu_keys, specific_menu_values):
                safe_set(key, value)

        # Write config with robust error handling
        try:
            # Validate config before writing
            if not isinstance(config, dict):
                config = {"theme": {}}

            # Write back with proper formatting
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)

        except Exception as write_error:
            print(f"Warning: Could not write hyprpanel config: {write_error}")
            # Try to write a minimal working config
            try:
                minimal_config = {
                    "theme": {
                        "bar": {
                            "background": theme['bg'],
                            "buttons": {
                                "background": theme['bg'],
                                "text": text_color
                            }
                        }
                    }
                }
                with open(config_path, 'w') as f:
                    json.dump(minimal_config, f, indent=2, ensure_ascii=False)
            except:
                pass  # Give up if even minimal config fails

    except Exception as e:
        print(f"Warning: Could not update hyprpanel config: {e}")
        pass  # Skip if config is malformed

def reload_applications():
    """Hot reload applications with proper async handling."""
    import time

    # 1. Hot reload Foot terminals (SIGUSR1 signal)
    try:
        # Send SIGUSR1 to all foot processes to reload config without restarting
        subprocess.run(['pkill', '-SIGUSR1', 'foot'], check=False)
    except:
        pass

    # 2. Restart Hyprpanel safely (kill gjs and restart)
    try:
        # Kill existing hyprpanel/gjs processes
        subprocess.run(['pkill', '-TERM', 'hyprpanel'], check=False)
        subprocess.run(['pkill', '-TERM', 'gjs'], check=False)

        # Wait for processes to terminate (max 2 seconds)
        max_wait = 20  # 2 seconds in 0.1s intervals
        wait_count = 0

        while wait_count < max_wait:
            # Check if processes are still running
            hyprpanel_check = subprocess.run(['pgrep', 'hyprpanel'],
                                           capture_output=True, check=False)
            gjs_check = subprocess.run(['pgrep', 'gjs'],
                                     capture_output=True, check=False)

            if hyprpanel_check.returncode != 0 and gjs_check.returncode != 0:
                break  # Both processes terminated

            time.sleep(0.1)
            wait_count += 1

        # Force kill if still running
        if wait_count >= max_wait:
            subprocess.run(['pkill', '-KILL', 'hyprpanel'], check=False)
            subprocess.run(['pkill', '-KILL', 'gjs'], check=False)
            time.sleep(0.2)

        # Start hyprpanel in background (async)
        subprocess.Popen(['hyprpanel'],
                        stdout=subprocess.DEVNULL,
                        stderr=subprocess.DEVNULL,
                        start_new_session=True)

    except Exception as e:
        # Fallback: simple restart
        try:
            subprocess.run(['pkill', 'hyprpanel'], check=False)
            time.sleep(0.5)
            subprocess.Popen(['hyprpanel'],
                           stdout=subprocess.DEVNULL,
                           stderr=subprocess.DEVNULL)
        except:
            pass

    # 3. Reload Hyprland configuration
    try:
        subprocess.run(['hyprctl', 'reload'], check=False)
    except:
        pass

def save_current_theme(theme_name):
    """Save current theme to state file."""
    state_file = Path.home() / ".config/current-theme"
    with open(state_file, 'w') as f:
        f.write(theme_name)

def get_current_theme():
    """Get current theme from state file."""
    state_file = Path.home() / ".config/current-theme"
    if state_file.exists():
        with open(state_file, 'r') as f:
            return f.read().strip()
    return "gruvbox-dark"

def main():
    if len(sys.argv) < 2:
        print("Usage: theme-fast <theme-name>")
        print("Available themes:", ", ".join(THEMES.keys()))
        print("Current theme:", get_current_theme())
        sys.exit(1)

    theme_name = sys.argv[1]

    # Handle help flags
    if theme_name in ["-h", "--help", "help"]:
        print("Theme Fast - Quick theme switcher")
        print()
        print("Usage: theme-fast <theme-name>")
        print("       theme-fast list")
        print("       theme-fast toggle")
        print("       theme-fast -h|--help")
        print()
        print("Available themes:", ", ".join(THEMES.keys()))
        print("Current theme:", get_current_theme())
        sys.exit(0)

    # Handle special commands
    if theme_name == "list":
        for name in THEMES.keys():
            current = " (current)" if name == get_current_theme() else ""
            print(f"{name}{current}")
        sys.exit(0)

    if theme_name == "toggle":
        current = get_current_theme()
        if current.endswith("-dark"):
            new_theme = current.replace("-dark", "-light")
        elif current.endswith("-light"):
            new_theme = current.replace("-light", "-dark")
        else:
            new_theme = "gruvbox-light" if current == "gruvbox-dark" else "gruvbox-dark"

        if new_theme in THEMES:
            theme_name = new_theme
        else:
            print(f"No variant available for {current}")
            sys.exit(1)

    if theme_name not in THEMES:
        print(f"Theme '{theme_name}' not found")
        print("Available themes:", ", ".join(THEMES.keys()))
        sys.exit(1)

    theme = THEMES[theme_name]

    # Apply theme - fast with proper reloading
    apply_foot_theme(theme)
    apply_hyprland_theme(theme)
    apply_hyprpanel_theme(theme, theme_name)  # Apply hyprpanel colors
    apply_p10k_theme(theme, theme_name)
    reload_applications()  # Hot reload foot and restart hyprpanel
    save_current_theme(theme_name)

    print(f"Applied theme: {theme_name}")

if __name__ == "__main__":
    main()
