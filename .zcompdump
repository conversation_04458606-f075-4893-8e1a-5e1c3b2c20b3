#files: 1713	version: 5.9

_comps=(
'-' '_precommand'
'.' '_source'
'5g' '_go'
'5l' '_go'
'6g' '_go'
'6l' '_go'
'8g' '_go'
'8l' '_go'
'a2ps' '_a2ps'
'aaaa' '_hosts'
'aap' '_aap'
'abcde' '_abcde'
'ack' '_ack'
'ack2' '_ack'
'ack-grep' '_ack'
'ack-standalone' '_ack'
'acpi' '_acpi'
'acpitool' '_acpitool'
'acroread' '_acroread'
'adb' '_adb'
'add-zle-hook-widget' '_add-zle-hook-widget'
'add-zsh-hook' '_add-zsh-hook'
'admin' '_sccs'
'afew' '_afew'
'age' '_age'
'alacritty' '_alacritty'
'ali' '_mh'
'alias' '_alias'
'amaya' '_webbrowser'
'analyseplugin' '_analyseplugin'
'android' '_android'
'animate' '_imagemagick'
'anno' '_mh'
'ansible' '_ansible'
'ansible-config' '_ansible'
'ansible-console' '_ansible'
'ansible-doc' '_ansible'
'ansible-galaxy' '_ansible'
'ansible-inventory' '_ansible'
'ansible-playbook' '_ansible'
'ansible-pull' '_ansible'
'ansible-vault' '_ansible'
'ant' '_ant'
'antiword' '_antiword'
'aodh' '_openstack'
'aoss' '_precommand'
'apache2ctl' '_apachectl'
'apachectl' '_apachectl'
'aplay' '_alsa-utils'
'apm' '_apm'
'appletviewer' '_java'
'apropos' '_man'
'apvlv' '_pdf'
'archlinux-java' '_archlinux-java'
'arduino-ctags' '_ctags'
'arecord' '_alsa-utils'
'arena' '_webbrowser'
'_arguments' '__arguments'
'arp' '_arp'
'arping' '_arping'
'-array-value-' '_value'
'artisan' '_artisan'
'asciidoctor' '_asciidoctor'
'asciinema' '_asciinema'
'ash' '_sh'
'-assign-parameter-' '_assign'
'at' '_at'
'atach' '_atach'
'atq' '_at'
'atrm' '_at'
'attr' '_attr'
'atuin' '_atuin'
'augtool' '_augeas'
'autoload' '_typeset'
'avahi-browse' '_avahi'
'avahi-browse-domains' '_avahi'
'avahi-resolve' '_avahi'
'avahi-resolve-address' '_avahi'
'avahi-resolve-host-name' '_avahi'
'avdmanager' '_avdmanager'
'awk' '_awk'
'b2sum' '_md5sum'
'barbican' '_openstack'
'base32' '_base64'
'base64' '_base64'
'basename' '_basename'
'basenc' '_basenc'
'bash' '_bash'
'bat' '_bat'
'batch' '_at'
'baz' '_baz'
'beep' '_beep'
'bg' '_jobs_bg'
'bibtex' '_bibtex'
'bindkey' '_bindkey'
'bison' '_bison'
'bitcoin-cli' '_bitcoin-cli'
'bluetoothctl' '_bluetoothctl'
'bmake' '_make'
'bogofilter' '_bogofilter'
'bogotune' '_bogofilter'
'bogoutil' '_bogofilter'
'bootctl' '_bootctl'
'bower' '_bower'
'bpython' '_bpython'
'bpython2' '_bpython'
'bpython2-gtk' '_bpython'
'bpython2-urwid' '_bpython'
'bpython3' '_bpython'
'bpython3-gtk' '_bpython'
'bpython3-urwid' '_bpython'
'bpython-gtk' '_bpython'
'bpython-urwid' '_bpython'
'-brace-parameter-' '_brace_parameter'
'brctl' '_brctl'
'bsdgrep' '_grep'
'bsdtar' '_tar'
'btdownloadcurses' '_bittorrent'
'btdownloadgui' '_bittorrent'
'btdownloadheadless' '_bittorrent'
'btlaunchmany' '_bittorrent'
'btlaunchmanycurses' '_bittorrent'
'btmakemetafile' '_bittorrent'
'btreannounce' '_bittorrent'
'btrename' '_bittorrent'
'btrfs' '_btrfs'
'btshowmetainfo' '_bittorrent'
'bttrack' '_bittorrent'
'buildhash' '_ispell'
'builtin' '_builtin'
'bundle' '_bundle'
'bunzip2' '_bzip2'
'burst' '_mh'
'busctl' '_busctl'
'bwrap' '_bwrap'
'bzcat' '_bzip2'
'bzegrep' '_grep'
'bzfgrep' '_grep'
'bzgrep' '_grep'
'bzip2' '_bzip2'
'bzip2recover' '_bzip2'
'bzr' '_bzr'
'c++' '_gcc'
'cabal' '_cabal'
'cal' '_cal'
'calendar' '_calendar'
'cap' '_cap'
'cargo' '_cargo'
'cask' '_cask'
'cat' '_cat'
'catchsegv' '_precommand'
'cc' '_gcc'
'ccache' '_ccache'
'ccal' '_ccal'
'cd' '_cd'
'cdc' '_sccs'
'cdcd' '_cdcd'
'cdr' '_cdr'
'cdrdao' '_cdrdao'
'cdrecord' '_cdrecord'
'ceilometer' '_openstack'
'certtool' '_gnutls'
'cf' '_cf'
'cftp' '_twisted'
'chage' '_users'
'chatblade' '_chatblade'
'chattr' '_chattr'
'chcon' '_chcon'
'chdir' '_cd'
'checkupdates' '_checkupdates'
'chfn' '_users'
'chgrp' '_chown'
'chimera' '_webbrowser'
'chkconfig' '_chkconfig'
'chkstow' '_stow'
'chmod' '_chmod'
'choc' '_choc'
'choom' '_choom'
'chown' '_chown'
'chpass' '_chsh'
'chromium' '_chromium'
'chroot' '_chroot'
'chrt' '_chrt'
'chsh' '_chsh'
'chwd' '_chwd'
'ci' '_rcs'
'cifsiostat' '_sysstat'
'cinder' '_openstack'
'ckeygen' '_twisted'
'cksum' '_cksum'
'clang' '_gcc'
'clang++' '_gcc'
'clang-check' '_clang-check'
'clang-format' '_clang-format'
'clang-tidy' '_clang-tidy'
'clay' '_clay'
'clear' '_nothing'
'cloudkitty' '_openstack'
'clusterdb' '_postgresql'
'cmake' '_cmake'
'cmp' '_cmp'
'co' '_rcs'
'code' '_code'
'coffee' '_coffee'
'column' '_column'
'comb' '_sccs'
'combine' '_imagemagick'
'combinediff' '_patchutils'
'comm' '_comm'
'-command-' '_autocd'
'command' '_command'
'-command-line-' '_normal'
'comp' '_mh'
'compadd' '_compadd'
'compdef' '_compdef'
'composer' '_composer'
'composer.phar' '_composer'
'composite' '_imagemagick'
'compress' '_compress'
'conan' '_conan'
'conch' '_twisted'
'concourse' '_concourse'
'-condition-' '_condition'
'config.status' '_configure'
'configure' '_configure'
'console' '_console'
'convert' '_imagemagick'
'coredumpctl' '_coredumpctl'
'cowsay' '_cowsay'
'cowthink' '_cowsay'
'cp' '_cp'
'cpio' '_cpio'
'cplay' '_cplay'
'cppcheck' '_cppcheck'
'cpupower' '_cpupower'
'createdb' '_pgsql_utils'
'createuser' '_pgsql_utils'
'crontab' '_crontab'
'crsh' '_cssh'
'cryptsetup' '_cryptsetup'
'cscope' '_cscope'
'csh' '_sh'
'csplit' '_csplit'
'cssh' '_cssh'
'ctags' '_ctags'
'ctags-exuberant' '_ctags'
'ctags-universal' '_ctags'
'ctr' '_ctr'
'curl' '_curl'
'cut' '_cut'
'cvs' '_cvs'
'dad' '_dad'
'darcs' '_darcs'
'dart' '_dart'
'dash' '_sh'
'date' '_date'
'dbus-launch' '_dbus'
'dbus-monitor' '_dbus'
'dbus-send' '_dbus'
'dconf' '_dconf'
'dcop' '_dcop'
'dcopclient' '_dcop'
'dcopfind' '_dcop'
'dcopobject' '_dcop'
'dcopref' '_dcop'
'dcopstart' '_dcop'
'dd' '_dd'
'declare' '_typeset'
'-default-' '_default'
'delta' '_sccs'
'designate' '_openstack'
'devtodo' '_devtodo'
'df' '_df'
'dget' '_dget'
'dhclient' '_dhclient'
'dhclient3' '_dhclient'
'dhcpcd' '_dhcpcd'
'diana' '_diana'
'dict' '_dict'
'diff' '_diff'
'diff3' '_diff3'
'diffstat' '_diffstat'
'dig' '_dig'
'dillo' '_webbrowser'
'dircmp' '_directories'
'direnv' '_direnv'
'dirs' '_dirs'
'disable' '_disable'
'disown' '_jobs_fg'
'display' '_imagemagick'
'dist' '_mh'
'django-admin' '_django'
'django-admin.py' '_django'
'dkms' '_dkms'
'dmake' '_make'
'dmesg' '_dmesg'
'dmidecode' '_dmidecode'
'doas' '_doas'
'docker' '_docker'
'docpad' '_docpad'
'domainname' '_yp'
'do-release-upgrade' '_do-release-upgrade'
'dos2unix' '_dos2unix'
'drill' '_drill'
'dropbox' '_dropbox'
'dropdb' '_pgsql_utils'
'dropuser' '_pgsql_utils'
'drush' '_drush'
'dsh' '_dsh'
'dtruss' '_dtruss'
'du' '_du'
'dvibook' '_dvi'
'dviconcat' '_dvi'
'dvicopy' '_dvi'
'dvidvi' '_dvi'
'dvipdf' '_dvi'
'dvips' '_dvi'
'dviselect' '_dvi'
'dvitodvi' '_dvi'
'dvitype' '_dvi'
'dwb' '_webbrowser'
'e2label' '_e2label'
'eatmydata' '_precommand'
'ecasound' '_ecasound'
'ecdsautil' '_ecdsautil'
'echotc' '_echotc'
'echoti' '_echoti'
'ed' '_ed'
'egrep' '_grep'
'elfdump' '_elfdump'
'elinks' '_elinks'
'emacs' '_emacs'
'emacsclient' '_emacsclient'
'emulate' '_emulate'
'emulator' '_emulator'
'enable' '_enable'
'enscript' '_enscript'
'entr' '_entr'
'env' '_env'
'envdir' '_envdir'
'eog' '_eog'
'epdfview' '_pdf'
'epsffit' '_psutils'
'-equal-' '_equal'
'erb' '_ruby'
'espeak' '_espeak'
'etags' '_etags'
'ethtool' '_ethtool'
'eu-nm' '_nm'
'eu-objdump' '_objdump'
'eu-readelf' '_readelf'
'eu-strings' '_strings'
'eval' '_precommand'
'eview' '_vim'
'evim' '_vim'
'evince' '_evince'
'ex' '_vi'
'exec' '_exec'
'expand' '_unexpand'
'export' '_typeset'
'exportfs' '_exportfs'
'express' '_webbrowser'
'extcheck' '_java'
'extractres' '_psutils'
'eza' '_eza'
'fab' '_fab'
'fail2ban-client' '_fail2ban-client'
'fakeroot' '_fakeroot'
'false' '_nothing'
'fastfetch' '_fastfetch'
'fast-theme' '_fast-theme'
'fc' '_fc'
'fc-list' '_xft_fonts'
'fc-match' '_xft_fonts'
'fd' '_fd'
'feh' '_feh'
'fetchmail' '_fetchmail'
'ffind' '_ffind'
'ffmpeg' '_ffmpeg'
'fg' '_jobs_fg'
'fgrep' '_grep'
'figlet' '_figlet'
'filterdiff' '_patchutils'
'find' '_find'
'findaffix' '_ispell'
'findmnt' '_findmnt'
'finger' '_finger'
'firefox' '_mozilla'
'-first-' '_first'
'fixdlsrps' '_psutils'
'fixfmps' '_psutils'
'fixmacps' '_psutils'
'fixpsditps' '_psutils'
'fixpspps' '_psutils'
'fixscribeps' '_psutils'
'fixtpps' '_psutils'
'fixwfwps' '_psutils'
'fixwpps' '_psutils'
'fixwwps' '_psutils'
'flac' '_flac'
'flatpak' '_flatpak'
'fleetctl' '_fleetctl'
'flex' '_flex'
'flex++' '_flex'
'flipdiff' '_patchutils'
'flist' '_mh'
'flists' '_mh'
'float' '_typeset'
'flutter' '_flutter'
'fly' '_concourse'
'fmt' '_fmt'
'fmttest' '_mh'
'fned' '_zed'
'fnext' '_mh'
'fold' '_fold'
'folder' '_mh'
'folders' '_mh'
'foot' '_foot'
'footclient' '_footclient'
'fortune' '_fortune'
'forw' '_mh'
'fprev' '_mh'
'free' '_free'
'freebsd-make' '_make'
'freezer' '_openstack'
'fsh' '_fsh'
'ftp' '_hosts'
'functions' '_typeset'
'fuser' '_fuser'
'fusermount' '_fusermount'
'fvm' '_fvm'
'fwhois' '_whois'
'fwupdmgr' '_fwupdmgr'
'fzf' '_fzf'
'g++' '_gcc'
'galeon' '_webbrowser'
'gas' '_gas'
'gawk' '_awk'
'gb2sum' '_md5sum'
'gbase32' '_base64'
'gbase64' '_base64'
'gbasename' '_basename'
'gcat' '_cat'
'gcc' '_gcc'
'gccgo' '_go'
'gchgrp' '_chown'
'gchmod' '_chmod'
'gchown' '_chown'
'gchroot' '_chroot'
'gcksum' '_cksum'
'gcmp' '_cmp'
'gcomm' '_comm'
'gcore' '_gcore'
'gcp' '_cp'
'gcut' '_cut'
'gdate' '_date'
'gdb' '_gdb'
'gdd' '_dd'
'gdf' '_df'
'gdiff' '_diff'
'gdu' '_du'
'geany' '_geany'
'gegrep' '_grep'
'gem' '_gem'
'genisoimage' '_genisoimage'
'genv' '_env'
'get' '_sccs'
'getafm' '_psutils'
'getconf' '_getconf'
'getent' '_getent'
'getfacl' '_getfacl'
'getfacl.exe' '_getfacl'
'getfattr' '_attr'
'getmail' '_getmail'
'getopt' '_getopt'
'getopts' '_vars'
'gex' '_vim'
'gexpand' '_unexpand'
'gfgrep' '_grep'
'gfind' '_find'
'gfmt' '_fmt'
'gfold' '_fold'
'ggetopt' '_getopt'
'ggrep' '_grep'
'ggv' '_gnome-gv'
'gh' '_gh'
'ghc' '_ghc'
'ghci' '_ghc'
'ghc-pkg' '_ghc'
'ghead' '_head'
'ghostscript' '_ghostscript'
'ghostview' '_pspdf'
'gid' '_id'
'ginstall' '_install'
'gist' '_gist'
'git' '_git'
'git-cvsserver' '_git'
'git-flow' '_git-flow'
'gitk' '_git'
'git-pulls' '_git-pulls'
'git-receive-pack' '_git'
'git-revise' '_git-revise'
'git-shell' '_git'
'git-upload-archive' '_git'
'git-upload-pack' '_git'
'git-wtf' '_git-wtf'
'gjoin' '_join'
'glance' '_openstack'
'glances' '_glances'
'gln' '_ln'
'global' '_global'
'glocate' '_locate'
'gls' '_ls'
'gm' '_graphicsmagick'
'gmake' '_make'
'gmd5sum' '_md5sum'
'gmkdir' '_mkdir'
'gmkfifo' '_mkfifo'
'gmknod' '_mknod'
'gmktemp' '_mktemp'
'gmplayer' '_mplayer'
'gmv' '_mv'
'gnl' '_nl'
'gnocchi' '_openstack'
'gnome-gv' '_gnome-gv'
'gnumfmt' '_numfmt'
'gnupod_addsong' '_gnupod'
'gnupod_addsong.pl' '_gnupod'
'gnupod_check' '_gnupod'
'gnupod_check.pl' '_gnupod'
'gnupod_INIT' '_gnupod'
'gnupod_INIT.pl' '_gnupod'
'gnupod_search' '_gnupod'
'gnupod_search.pl' '_gnupod'
'gnutls-cli' '_gnutls'
'gnutls-cli-debug' '_gnutls'
'gnutls-serv' '_gnutls'
'go' '_golang'
'god' '_od'
'gofmt' '_go'
'google' '_google'
'gpasswd' '_gpasswd'
'gpaste' '_paste'
'gpatch' '_patch'
'gpg' '_gpg'
'gpg2' '_gpg'
'gpgconf' '_gpgconf'
'gpgv' '_gpg'
'gpg-zip' '_gpg'
'gphoto2' '_gphoto2'
'gprintenv' '_printenv'
'gprof' '_gprof'
'gqview' '_gqview'
'gradle' '_gradle'
'gradlew' '_gradle'
'grail' '_webbrowser'
'greadlink' '_readlink'
'grep' '_grep'
'grepdiff' '_patchutils'
'grm' '_rm'
'grmdir' '_rmdir'
'groff' '_groff'
'groupadd' '_user_admin'
'groupdel' '_groups'
'groupmod' '_user_admin'
'groups' '_users'
'growisofs' '_growisofs'
'grpcurl' '_grpcurl'
'gs' '_ghostscript'
'gsbj' '_pspdf'
'gsdj' '_pspdf'
'gsdj500' '_pspdf'
'gsed' '_sed'
'gseq' '_seq'
'gsettings' '_gsettings'
'gsha1sum' '_md5sum'
'gsha224sum' '_md5sum'
'gsha256sum' '_md5sum'
'gsha384sum' '_md5sum'
'gsha512sum' '_md5sum'
'gshred' '_shred'
'gshuf' '_shuf'
'gslj' '_pspdf'
'gslp' '_pspdf'
'gsnd' '_pspdf'
'gsort' '_sort'
'gsplit' '_split'
'gstat' '_stat'
'gstdbuf' '_stdbuf'
'gstrings' '_strings'
'gstty' '_stty'
'gsum' '_cksum'
'gtac' '_tac'
'gtail' '_tail'
'gtar' '_tar'
'gtee' '_tee'
'gtimeout' '_timeout'
'gtk4-launch' '_gtk-launch'
'gtk-launch' '_gtk-launch'
'gtouch' '_touch'
'gtr' '_tr'
'gtty' '_tty'
'guilt' '_guilt'
'guilt-add' '_guilt'
'guilt-applied' '_guilt'
'guilt-delete' '_guilt'
'guilt-files' '_guilt'
'guilt-fold' '_guilt'
'guilt-fork' '_guilt'
'guilt-header' '_guilt'
'guilt-help' '_guilt'
'guilt-import' '_guilt'
'guilt-import-commit' '_guilt'
'guilt-init' '_guilt'
'guilt-new' '_guilt'
'guilt-next' '_guilt'
'guilt-patchbomb' '_guilt'
'guilt-pop' '_guilt'
'guilt-prev' '_guilt'
'guilt-push' '_guilt'
'guilt-rebase' '_guilt'
'guilt-refresh' '_guilt'
'guilt-rm' '_guilt'
'guilt-series' '_guilt'
'guilt-status' '_guilt'
'guilt-top' '_guilt'
'guilt-unapplied' '_guilt'
'guname' '_uname'
'gunexpand' '_unexpand'
'guniq' '_uniq'
'gunzip' '_gzip'
'guptime' '_uptime'
'gv' '_gv'
'gview' '_vim'
'gvim' '_vim'
'gvimdiff' '_vim'
'gwc' '_wc'
'gwho' '_who'
'gxargs' '_xargs'
'gzcat' '_gzip'
'gzegrep' '_grep'
'gzfgrep' '_grep'
'gzgrep' '_grep'
'gzilla' '_webbrowser'
'gzip' '_gzip'
'hash' '_hash'
'hd' '_hexdump'
'head' '_head'
'heat' '_openstack'
'hello' '_hello'
'help' '_sccs'
'hexdump' '_hexdump'
'hilite' '_precommand'
'histed' '_zed'
'history' '_fc'
'hledger' '_hledger'
'homestead' '_homestead'
'host' '_host'
'hostname' '_hostname'
'hostnamectl' '_hostnamectl'
'hotjava' '_webbrowser'
'htop' '_htop'
'http' '_httpie'
'https' '_httpie'
'hyprctl' '_hyprctl'
'hyprpm' '_hyprpm'
'ibus' '_ibus'
'iceweasel' '_mozilla'
'icombine' '_ispell'
'iconv' '_iconv'
'iconvconfig' '_iconvconfig'
'id' '_id'
'identify' '_imagemagick'
'ifconfig' '_ifconfig'
'ifdown' '_net_interfaces'
'iftop' '_iftop'
'ifup' '_net_interfaces'
'ijoin' '_ispell'
'img2sixel' '_img2sixel'
'import' '_imagemagick'
'inc' '_mh'
'includeres' '_psutils'
'include-what-you-use' '_include-what-you-use'
'info' '_texinfo'
'infocmp' '_terminals'
'initctl' '_initctl'
'initdb' '_pgsql_utils'
'insmod' '_insmod'
'install' '_install'
'install-info' '_texinfo'
'integer' '_typeset'
'interdiff' '_patchutils'
'inxi' '_inxi'
'ionice' '_ionice'
'iostat' '_iostat'
'ip' '_ip'
'ip6tables' '_iptables'
'ip6tables-restore' '_iptables'
'ip6tables-save' '_iptables'
'ipkg' '_opkg'
'ipsec' '_ipsec'
'ipset' '_ipset'
'iptables' '_iptables'
'iptables-restore' '_iptables'
'iptables-save' '_iptables'
'irb' '_ruby'
'ironic' '_openstack'
'irssi' '_irssi'
'isag' '_sysstat'
'ispell' '_ispell'
'iwconfig' '_iwconfig'
'iwyu' '_include-what-you-use'
'jadetex' '_tex'
'jar' '_java'
'jarsigner' '_java'
'java' '_java'
'javac' '_java'
'javadoc' '_java'
'javah' '_java'
'javap' '_java'
'jdb' '_java'
'jest' '_jest'
'jest.js' '_jest'
'jmeter' '_jmeter'
'jmeter-plugins' '_jmeter-plugins'
'jobs' '_jobs_builtin'
'joe' '_joe'
'join' '_join'
'jonas' '_jonas'
'journalctl' '_journalctl'
'jq' '_jq'
'jrnl' '_jrnl'
'kak' '_kak'
'kdeconnect-cli' '_kdeconnect'
'kernel-install' '_kernel-install'
'keyring' '_keyring'
'keystone' '_openstack'
'keytool' '_java'
'kfmclient' '_kfmclient'
'kill' '_kill'
'killall' '_killall'
'killall5' '_killall'
'kioclient' '_kfmclient'
'kitchen' '_kitchen'
'kitty' '_kitty'
'knife' '_knife'
'knock' '_knock'
'konqueror' '_webbrowser'
'kpartx' '_kpartx'
'kpdf' '_pdf'
'ksh' '_sh'
'ksh88' '_sh'
'ksh93' '_sh'
'kvno' '_kvno'
'l3build' '_l3build'
'last' '_last'
'lastb' '_last'
'latex' '_tex'
'latexmk' '_tex'
'ldconfig' '_ldconfig'
'ldconfig.real' '_ldconfig'
'ldd' '_ldd'
'less' '_less'
'let' '_math'
'lftp' '_ncftp'
'lha' '_lha'
'libinput' '_libinput'
'light' '_webbrowser'
'lilypond' '_lilypond'
'limit' '_limit'
'links' '_links'
'links2' '_links'
'linux' '_uml'
'lldb' '_lldb'
'llvm-g++' '_gcc'
'llvm-gcc' '_gcc'
'llvm-objdump' '_objdump'
'ln' '_ln'
'loadkeys' '_loadkeys'
'local' '_typeset'
'locale' '_locale'
'localectl' '_localectl'
'localedef' '_localedef'
'locate' '_locate'
'log' '_nothing'
'logger' '_logger'
'loginctl' '_loginctl'
'logname' '_nothing'
'look' '_look'
'losetup' '_losetup'
'lp' '_lp'
'lpadmin' '_lp'
'lpinfo' '_lp'
'lpoptions' '_lp'
'lpq' '_lp'
'lpr' '_lp'
'lprm' '_lp'
'lpstat' '_lp'
'ls' '_ls'
'lsattr' '_lsattr'
'lsblk' '_lsblk'
'lscpu' '_lscpu'
'lsdiff' '_patchutils'
'lsinitcpio' '_mkinitcpio'
'lsmod' '_lsmod'
'lsns' '_lsns'
'lsof' '_lsof'
'lsusb' '_lsusb'
'ltrace' '_ltrace'
'lua' '_lua'
'luarocks' '_luarocks'
'lunchy' '_lunchy'
'lynx' '_lynx'
'lz4' '_lz4'
'lz4c' '_lz4'
'lz4c32' '_lz4'
'lz4cat' '_lz4'
'lzcat' '_xz'
'lzma' '_xz'
'lzop' '_lzop'
'mac2unix' '_dos2unix'
'machinectl' '_machinectl'
'magnum' '_openstack'
'mail' '_mail'
'Mail' '_mail'
'mailx' '_mail'
'make' '_make'
'makeinfo' '_texinfo'
'makepkg' '_pacman'
'man' '_man'
'manage.py' '_django'
'manila' '_openstack'
'mark' '_mh'
'mat' '_mat'
'mat2' '_mat2'
'-math-' '_math'
'matlab' '_matlab'
'mattrib' '_mtools'
'mc' '_mc'
'mcd' '_mtools'
'mcopy' '_mtools'
'md2' '_cksum'
'md4' '_cksum'
'md5' '_cksum'
'md5sum' '_md5sum'
'mdadm' '_mdadm'
'mdel' '_mtools'
'mdeltree' '_mtools'
'mdir' '_mtools'
'mdu' '_mtools'
'mencal' '_mencal'
'mere' '_mere'
'merge' '_rcs'
'meson' '_meson'
'metaflac' '_flac'
'mformat' '_mtools'
'mgv' '_pspdf'
'mhfixmsg' '_mh'
'mhlist' '_mh'
'mhmail' '_mh'
'mhn' '_mh'
'mhparam' '_mh'
'mhpath' '_mh'
'mhshow' '_mh'
'mhstore' '_mh'
'middleman' '_middleman'
'mii-tool' '_mii-tool'
'mina' '_mina'
'mistral' '_openstack'
'mix' '_mix'
'mkcert' '_mkcert'
'mkdir' '_mkdir'
'mkfifo' '_mkfifo'
'mkinitcpio' '_mkinitcpio'
'mkisofs' '_growisofs'
'mknod' '_mknod'
'mksh' '_sh'
'mktemp' '_mktemp'
'mktunes' '_gnupod'
'mktunes.pl' '_gnupod'
'mlabel' '_mtools'
'mlocate' '_locate'
'mmd' '_mtools'
'mmm' '_webbrowser'
'mmount' '_mtools'
'mmove' '_mtools'
'modinfo' '_modutils'
'modprobe' '_modutils'
'module' '_module'
'mogrify' '_imagemagick'
'monasca' '_openstack'
'mondoarchive' '_mondo'
'montage' '_imagemagick'
'moosic' '_moosic'
'Mosaic' '_webbrowser'
'mosh' '_mosh'
'mount' '_mount'
'mozilla' '_mozilla'
'mozilla-firefox' '_mozilla'
'mozilla-xremote-client' '_mozilla'
'mpc' '_mpc'
'mplayer' '_mplayer'
'mpstat' '_sysstat'
'mpv' '_mpv'
'mr' '_myrepos'
'mrd' '_mtools'
'mread' '_mtools'
'mren' '_mtools'
'msgchk' '_mh'
'mssh' '_mssh'
'mt' '_mt'
'mtn' '_monotone'
'mtoolstest' '_mtools'
'mtr' '_mtr'
'mtype' '_mtools'
'munchlist' '_ispell'
'mupdf' '_mupdf'
'murano' '_openstack'
'mush' '_mail'
'mussh' '_mussh'
'mutt' '_mutt'
'mux' '_tmuxinator'
'mv' '_mv'
'mvim' '_vim'
'mvn' '_mvn'
'mvnDebug' '_mvn'
'mx' '_hosts'
'mysql' '_mysql_utils'
'mysqladmin' '_mysql_utils'
'mysqldiff' '_mysqldiff'
'mysqldump' '_mysql_utils'
'mysqlimport' '_mysql_utils'
'mysqlshow' '_mysql_utils'
'nail' '_mail'
'nano' '_nano'
'nanoc' '_nanoc'
'native2ascii' '_java'
'nautilus' '_nautilus'
'nawk' '_awk'
'nc' '_netcat'
'ncal' '_cal'
'ncftp' '_ncftp'
'ncl' '_nedit'
'nedit' '_nedit'
'nedit-nc' '_nedit'
'neo' '_neo'
'neofetch' '_neofetch'
'netcat' '_netcat'
'netctl' '_netctl'
'netctl-auto' '_netctl'
'netrik' '_webbrowser'
'netscape' '_netscape'
'netstat' '_netstat'
'networkctl' '_networkctl'
'networkQuality' '_networkQuality'
'neutron' '_openstack'
'new' '_mh'
'newgrp' '_groups'
'next' '_mh'
'nft' '_nftables'
'nginx' '_nginx'
'ngrep' '_ngrep'
'nice' '_nice'
'ninja' '_ninja'
'nkf' '_nkf'
'nl' '_nl'
'nm' '_nm'
'nmap' '_nmap'
'nmblookup' '_samba'
'nmcli' '_networkmanager'
'nocorrect' '_precommand'
'node' '_node'
'noglob' '_precommand'
'nohup' '_precommand'
'nova' '_openstack'
'npm' '_npm'
'ns' '_hosts'
'nsenter' '_nsenter'
'nslookup' '_nslookup'
'ntalk' '_other_accounts'
'numfmt' '_numfmt'
'nvim' '_vim'
'nvm' '_nvm'
'objdump' '_objdump'
'od' '_od'
'ogg123' '_vorbis'
'oggdec' '_vorbis'
'oggenc' '_vorbis'
'ogginfo' '_vorbis'
'oksh' '_sh'
'okular' '_okular'
'oomctl' '_oomctl'
'openssl' '_openssl'
'openstack' '_openstack'
'openvpn3' '_openvpn3'
'opera' '_webbrowser'
'opera-next' '_webbrowser'
'opkg' '_opkg'
'optirun' '_optirun'
'opusdec' '_opustools'
'opusenc' '_opustools'
'opusinfo' '_opustools'
'p4' '_perforce'
'p4d' '_perforce'
'pacat' '_pulseaudio'
'paccache' '_paccache'
'pacdiff' '_pacdiff'
'pack' '_pack'
'packf' '_mh'
'paclist' '_paclist'
'paclog-pkglist' '_paclog-pkglist'
'pacman' '_pacman'
'pacman-conf' '_pacman'
'pacman-key' '_pacman'
'pacman.static' '_pacman'
'pacmd' '_pulseaudio'
'pacscripts' '_pacscripts'
'pacsearch' '_pacsearch'
'pacsort' '_pacsort'
'pactl' '_pulseaudio'
'pactree' '_pactree'
'padsp' '_pulseaudio'
'pandoc' '_pandoc'
'paplay' '_pulseaudio'
'parallel' '_parallel'
'-parameter-' '_parameter'
'parec' '_pulseaudio'
'parecord' '_pulseaudio'
'paru' '_paru'
'passwd' '_users'
'paste' '_paste'
'pasuspender' '_pulseaudio'
'patch' '_patch'
'patool' '_patool'
'pax' '_pax'
'pcat' '_pack'
'pcp-htop' '_htop'
'pcred' '_pids'
'pdf2dsc' '_pdf'
'pdf2ps' '_pdf'
'pdffonts' '_pdf'
'pdfimages' '_pdf'
'pdfinfo' '_pdf'
'pdfjadetex' '_tex'
'pdflatex' '_tex'
'pdfopt' '_pdf'
'pdftex' '_tex'
'pdftexi2dvi' '_texinfo'
'pdftk' '_pdftk'
'pdftopbm' '_pdf'
'pdftops' '_pdf'
'pdftotext' '_pdf'
'pdksh' '_sh'
'perf' '_perf'
'periscope' '_periscope'
'perl' '_perl'
'perldoc' '_perldoc'
'pfiles' '_pids'
'pflags' '_pids'
'pg_config' '_postgresql'
'pg_ctl' '_postgresql'
'pg_dump' '_pgsql_utils'
'pg_dumpall' '_pgsql_utils'
'pg_isready' '_postgresql'
'pgrep' '_pgrep'
'pg_restore' '_pgsql_utils'
'pg_upgrade' '_postgresql'
'phing' '_phing'
'php' '_php'
'pick' '_mh'
'picocom' '_picocom'
'pidof' '_pidof'
'pidstat' '_sysstat'
'pigz' '_gzip'
'pine' '_pine'
'pinef' '_pine'
'pinfo' '_texinfo'
'ping' '_ping'
'ping6' '_ping'
'pixz' '_pixz'
'pkcon' '_pkcon'
'pkgadd' '_pkgadd'
'pkg-config' '_pkg-config'
'pkgfile' '_pkgfile'
'pkginfo' '_pkginfo'
'pkgrm' '_pkgrm'
'pkill' '_pgrep'
'play' '_play'
'pldd' '_pids'
'pm2' '_pm2'
'pmake' '_make'
'pman' '_perl_modules'
'pmap' '_pmap'
'pmcat' '_perl_modules'
'pmdesc' '_perl_modules'
'pmeth' '_perl_modules'
'pmexp' '_perl_modules'
'pmfunc' '_perl_modules'
'pmload' '_perl_modules'
'pmls' '_perl_modules'
'pmpath' '_perl_modules'
'pmvers' '_perl_modules'
'podgrep' '_perl_modules'
'podpath' '_perl_modules'
'podtoc' '_perl_modules'
'poetry' '_poetry'
'poff' '_pon'
'policytool' '_java'
'pon' '_pon'
'popd' '_directory_stack'
'port' '_port'
'postconf' '_postfix'
'postgres' '_postgresql'
'postmaster' '_postgresql'
'postqueue' '_postfix'
'postsuper' '_postfix'
'powerprofilesctl' '_powerprofilesctl'
'pr' '_pr'
'pre-commit' '_pre-commit'
'prev' '_mh'
'print' '_print'
'printenv' '_printenv'
'printf' '_print'
'profile-sync-daemon' '_psd'
'prompt' '_prompt'
'protoc' '_protoc'
'prove' '_prove'
'prs' '_sccs'
'prt' '_sccs'
'prun' '_pids'
'ps' '_ps'
'ps2ascii' '_pspdf'
'ps2epsi' '_postscript'
'ps2pdf' '_postscript'
'ps2pdf12' '_postscript'
'ps2pdf13' '_postscript'
'ps2pdf14' '_postscript'
'ps2pdfwr' '_postscript'
'ps2ps' '_postscript'
'psbook' '_psutils'
'psd' '_psd'
'psed' '_sed'
'psig' '_pids'
'psmerge' '_psutils'
'psmulti' '_postscript'
'psnup' '_psutils'
'psql' '_pgsql_utils'
'psresize' '_psutils'
'psselect' '_psutils'
'pstack' '_pids'
'pstoedit' '_pspdf'
'pstop' '_pids'
'pstops' '_psutils'
'pstotgif' '_pspdf'
'pswrap' '_postscript'
'ptx' '_ptx'
'pulseaudio' '_pulseaudio'
'pump' '_pump'
'pushd' '_cd'
'pv' '_pv'
'pwait' '_pids'
'pwdx' '_pids'
'pwgen' '_pwgen'
'pygmentize' '_pygmentize'
'pyhtmlizer' '_twisted'
'pypr' '_pypr'
'qdbus' '_qdbus'
'qiv' '_qiv'
'qmk' '_qmk'
'qpdf' '_qpdf'
'quilt' '_quilt'
'r' '_fc'
'rails' '_rails'
'rake' '_rake'
'ralio' '_ralio'
'rankmirrors' '_rankmirrors'
'ranlib' '_ranlib'
'rar' '_rar'
'rc' '_sh'
'rclone' '_rclone'
'rcp' '_rlogin'
'rcs' '_rcs'
'rcsdiff' '_rcs'
'rdesktop' '_rdesktop'
'rdfind' '_rdfind'
'read' '_read'
'readelf' '_readelf'
'readlink' '_readlink'
'readonly' '_typeset'
'rec' '_redis-cli'
'-redirect-' '_redirect'
'-redirect-,<,bunzip2' '_bzip2'
'-redirect-,<,bzip2' '_bzip2'
'-redirect-,>,bzip2' '_bzip2'
'-redirect-,<,compress' '_compress'
'-redirect-,>,compress' '_compress'
'-redirect-,-default-,-default-' '_files'
'-redirect-,<,gunzip' '_gzip'
'-redirect-,<,gzip' '_gzip'
'-redirect-,>,gzip' '_gzip'
'-redirect-,<,uncompress' '_compress'
'-redirect-,<,unxz' '_xz'
'-redirect-,<,xz' '_xz'
'-redirect-,>,xz' '_xz'
'redis-cli' '_redis-cli'
'refile' '_mh'
'rehash' '_hash'
'reindexdb' '_postgresql'
'reload' '_initctl'
'remsh' '_rlogin'
'renice' '_renice'
'repl' '_mh'
'resolvectl' '_resolvectl'
'restart' '_initctl'
'retawq' '_webbrowser'
'rfkill' '_rfkill'
'rg' '_rg'
'rgrep' '_grep'
'rgview' '_vim'
'rgvim' '_vim'
'ri' '_ri'
'rkt' '_rkt'
'rlogin' '_rlogin'
'rm' '_rm'
'rmd160' '_cksum'
'rmdel' '_sccs'
'rmdir' '_rmdir'
'rmf' '_mh'
'rmic' '_java'
'rmid' '_java'
'rmiregistry' '_java'
'rmlint' '_rmlint'
'rmlint.sh' '_rmlint'
'rmm' '_mh'
'rmmod' '_rmmod'
'route' '_route'
'rrdtool' '_rrdtool'
'rsh' '_rlogin'
'rslsync' '_rslsync'
'rspec' '_rspec'
'rsvm' '_rsvm'
'rsync' '_rsync'
'rtin' '_tin'
'rubber' '_rubber'
'rubber-info' '_rubber'
'rubber-pipe' '_rubber'
'rubocop' '_rubocop'
'ruby' '_ruby'
'ruby-mri' '_ruby'
'run0' '_run0'
'run-help' '_run-help'
'rup' '_hosts'
'rusage' '_precommand'
'rustup' '_rustup'
'rview' '_vim'
'rvim' '_vim'
'rwho' '_hosts'
'rxvt' '_urxvt'
's2p' '_sed'
'sact' '_sccs'
'sadf' '_sysstat'
'sahara' '_openstack'
'sar' '_sysstat'
'sbt' '_sbt'
'scala' '_scala'
'scalac' '_scala'
'scan' '_mh'
'sccs' '_sccs'
'sccsdiff' '_sccs'
'sched' '_sched'
'schedtool' '_schedtool'
'scons' '_scons'
'scp' '_ssh'
'scrcpy' '_scrcpy'
'scrcpy.exe' '_scrcpy'
'screen' '_screen'
'screencapture' '_screencapture'
'script' '_script'
'scriptreplay' '_script'
'scrub' '_scrub'
'sd' '_sd'
'sdd' '_sdd'
'sdkmanager' '_sdkmanager'
'seaf-cli' '_seafile'
'sed' '_sed'
'senlin' '_openstack'
'seq' '_seq'
'serialver' '_java'
'service' '_service'
'set' '_set'
'setcap' '_setcap'
'setfacl' '_setfacl'
'setfacl.exe' '_setfacl'
'setfattr' '_attr'
'setopt' '_setopt'
'setpriv' '_setpriv'
'setsid' '_setsid'
'setup.py' '_setup.py'
'setxkbmap' '_setxkbmap'
'sfdx' '_sfdx'
'sftp' '_ssh'
'sh' '_sh'
'sha1' '_cksum'
'sha1sum' '_md5sum'
'sha224sum' '_md5sum'
'sha256' '_cksum'
'sha256sum' '_md5sum'
'sha384' '_cksum'
'sha384sum' '_md5sum'
'sha512' '_cksum'
'sha512sum' '_md5sum'
'sha512t256' '_cksum'
'shallow-backup' '_shallow-backup'
'shasum' '_shasum'
'shellcheck' '_shellcheck'
'shift' '_arrays'
'show' '_mh'
'showchar' '_psutils'
'showmount' '_showmount'
'showoff' '_showoff'
'shred' '_shred'
'shuf' '_shuf'
'shutdown' '_shutdown'
'sisu' '_sisu'
'skein1024' '_cksum'
'skein256' '_cksum'
'skein512' '_cksum'
'skipstone' '_webbrowser'
'slabtop' '_slabtop'
'slitex' '_tex'
'slocate' '_locate'
'slogin' '_ssh'
'slrn' '_slrn'
'smartctl' '_smartmontools'
'smbclient' '_samba'
'smbcontrol' '_samba'
'smbstatus' '_samba'
'soa' '_hosts'
'socket' '_socket'
'sort' '_sort'
'sortm' '_mh'
'source' '_source'
'spamassassin' '_spamassassin'
'split' '_split'
'splitdiff' '_patchutils'
'sqlite' '_sqlite'
'sqlite3' '_sqlite'
'sqsh' '_sqsh'
'sr' '_surfraw'
'srm' '_srm'
'srptool' '_gnutls'
'ss' '_ss'
'ssh' '_ssh'
'ssh-add' '_ssh'
'ssh-agent' '_ssh'
'ssh-copy-id' '_ssh'
'sshfs' '_sshfs'
'ssh-keygen' '_ssh'
'ssh-keyscan' '_ssh'
'stack' '_stack'
'star' '_tar'
'starship' '_starship'
'start' '_initctl'
'stat' '_stat'
'status' '_initctl'
'stdbuf' '_stdbuf'
'stg' '_stgit'
'stop' '_initctl'
'stow' '_stow'
'strace' '_strace'
'strace64' '_strace'
'strftime' '_strftime'
'strings' '_strings'
'strip' '_strip'
'strongswan' '_ipsec'
'stty' '_stty'
'su' '_su'
'subl' '_sublimetext'
'subliminal' '_subliminal'
'-subscript-' '_subscript'
'sudo' '_sudo'
'sudoedit' '_sudo'
'sum' '_cksum'
'supervisorctl' '_supervisorctl'
'supervisord' '_supervisord'
'surfraw' '_surfraw'
'sv' '_runit'
'svm' '_svm'
'svn' '_subversion'
'svnadmin' '_subversion'
'svnadmin-static' '_subversion'
'svnlite' '_subversion'
'svnliteadmin' '_subversion'
'swaks' '_swaks'
'swanctl' '_swanctl'
'swift' '_swift'
'swiftc' '_swift'
'swww' '_swww'
'sync' '_nothing'
'sysctl' '_sysctl'
'systemctl' '_systemctl'
'systemd-analyze' '_systemd-analyze'
'systemd-ask-password' '_systemd'
'systemd-cat' '_systemd'
'systemd-cgls' '_systemd'
'systemd-cgtop' '_systemd'
'systemd-delta' '_systemd-delta'
'systemd-detect-virt' '_systemd'
'systemd-inhibit' '_systemd-inhibit'
'systemd-machine-id-setup' '_systemd'
'systemd-notify' '_systemd'
'systemd-nspawn' '_systemd-nspawn'
'systemd-path' '_systemd-path'
'systemd-resolve' '_resolvectl'
'systemd-run' '_systemd-run'
'systemd-tmpfiles' '_systemd-tmpfiles'
'systemd-tty-ask-password-agent' '_systemd'
'tac' '_tac'
'tacker' '_openstack'
'tail' '_tail'
'talk' '_other_accounts'
'tar' '_tar'
'tardy' '_tardy'
'tcpdump' '_tcpdump'
'tcp_open' '_tcpsys'
'tcptraceroute' '_tcptraceroute'
'tcsh' '_sh'
'tda' '_devtodo'
'tdd' '_devtodo'
'tde' '_devtodo'
'tdr' '_devtodo'
'teamocil' '_teamocil'
'tee' '_tee'
'telnet' '_telnet'
'tex' '_tex'
'texi2any' '_texinfo'
'texi2dvi' '_texinfo'
'texi2pdf' '_texinfo'
'texindex' '_texinfo'
'tg' '_topgit'
'thor' '_thor'
'tidy' '_tidy'
'tig' '_git'
'-tilde-' '_tilde'
'time' '_precommand'
'timedatectl' '_timedatectl'
'timeout' '_timeout'
'times' '_nothing'
'tin' '_tin'
'tkconch' '_twisted'
'tkinfo' '_texinfo'
'tla' '_tla'
'tldr' '_tldr'
'tload' '_tload'
'tmux' '_tmux'
'tmuxinator' '_tmuxinator'
'tmuxp' '_tmuxp'
'todo' '_devtodo'
'todo.sh' '_todo.sh'
'toilet' '_toilet'
'top' '_top'
'totdconfig' '_totd'
'touch' '_touch'
'tox' '_tox'
'tpb' '_tpb'
'tput' '_tput'
'tr' '_tr'
'tracepath' '_tracepath'
'tracepath6' '_tracepath'
'traceroute' '_hosts'
'transmission-remote' '_transmission'
'trap' '_trap'
'tree' '_tree'
'trial' '_twisted'
'trove' '_openstack'
'true' '_nothing'
'truncate' '_truncate'
'truss' '_truss'
'tryaffix' '_ispell'
'tsc' '_tsc'
'ts-node' '_ts-node'
'tty' '_tty'
'ttyctl' '_ttyctl'
'tunctl' '_uml'
'tune2fs' '_tune2fs'
'tunes2pod' '_gnupod'
'tunes2pod.pl' '_gnupod'
'twidge' '_twidge'
'twist' '_twisted'
'twistd' '_twisted'
'txt' '_hosts'
'type' '_which'
'typeset' '_typeset'
'udevadm' '_udevadm'
'udisksctl' '_udisksctl'
'ufw' '_ufw'
'ulimit' '_ulimit'
'uml_mconsole' '_uml'
'uml_moo' '_uml'
'uml_switch' '_uml'
'umount' '_mount'
'unace' '_unace'
'unalias' '_aliases'
'uname' '_uname'
'uncompress' '_compress'
'unexpand' '_unexpand'
'unfunction' '_functions'
'unget' '_sccs'
'unhash' '_unhash'
'uniq' '_uniq'
'unison' '_unison'
'units' '_units'
'unix2dos' '_dos2unix'
'unix2mac' '_dos2unix'
'unlimit' '_limits'
'unlz4' '_lz4'
'unlzma' '_xz'
'unpack' '_pack'
'unpigz' '_gzip'
'unrar' '_rar'
'unset' '_vars'
'unsetopt' '_setopt'
'unshare' '_unshare'
'unwrapdiff' '_patchutils'
'unxz' '_xz'
'unzip' '_zip'
'updpkgsums' '_updpkgsums'
'upower' '_upower'
'uptime' '_uptime'
'urxvt' '_urxvt'
'urxvt256c' '_urxvt'
'urxvt256cc' '_urxvt'
'urxvt256c-ml' '_urxvt'
'urxvt256c-mlc' '_urxvt'
'urxvtc' '_urxvt'
'useradd' '_user_admin'
'userdel' '_users'
'usermod' '_user_admin'
'uuidgen' '_uuidgen'
'uv' '_uv'
'vacuumdb' '_pgsql_utils'
'val' '_sccs'
'valgrind' '_valgrind'
'-value-' '_value'
'-value-,ADB_TRACE,-default-' '_adb'
'-value-,ANDROID_LOG_TAGS,-default-' '_adb'
'-value-,ANDROID_SERIAL,-default-' '_adb'
'-value-,ANSIBLE_INVENTORY_ENABLED,-default-' '_ansible'
'-value-,ANSIBLE_STDOUT_CALLBACK,-default-' '_ansible'
'-value-,ANT_ARGS,-default-' '_ant'
'-value-,CFLAGS,-default-' '_gcc'
'-value-,CMAKE_GENERATOR,-default-' '_cmake'
'-value-,CPPFLAGS,-default-' '_gcc'
'-value-,CXXFLAGS,-default-' '_gcc'
'-value-,-default-,-command-' '_zargs'
'-value-,-default-,-default-' '_value'
'-value-,DISPLAY,-default-' '_x_display'
'-value-,GREP_OPTIONS,-default-' '_grep'
'-value-,GZIP,-default-' '_gzip'
'-value-,LANG,-default-' '_locales'
'-value-,LANGUAGE,-default-' '_locales'
'-value-,LD_DEBUG,-default-' '_ld_debug'
'-value-,LDFLAGS,-default-' '_gcc'
'-value-,LESSCHARSET,-default-' '_less'
'-value-,LESS,-default-' '_less'
'-value-,LOOPDEV_DEBUG,-default-' '_losetup'
'-value-,LPDEST,-default-' '_printers'
'-value-,MPD_HOST,-default' '_mpc'
'-value-,P4CLIENT,-default-' '_perforce'
'-value-,P4MERGE,-default-' '_perforce'
'-value-,P4PORT,-default-' '_perforce'
'-value-,P4USER,-default-' '_perforce'
'-value-,PERLDOC,-default-' '_perldoc'
'-value-,PRINTER,-default-' '_printers'
'-value-,PROMPT2,-default-' '_ps1234'
'-value-,PROMPT3,-default-' '_ps1234'
'-value-,PROMPT4,-default-' '_ps1234'
'-value-,PROMPT,-default-' '_ps1234'
'-value-,PS1,-default-' '_ps1234'
'-value-,PS2,-default-' '_ps1234'
'-value-,PS3,-default-' '_ps1234'
'-value-,PS4,-default-' '_ps1234'
'-value-,RPROMPT2,-default-' '_ps1234'
'-value-,RPROMPT,-default-' '_ps1234'
'-value-,RPS1,-default-' '_ps1234'
'-value-,RPS2,-default-' '_ps1234'
'-value-,SPROMPT,-default-' '_ps1234'
'-value-,TERM,-default-' '_terminals'
'-value-,TERMINFO_DIRS,-default-' '_dir_list'
'-value-,TZ,-default-' '_time_zone'
'-value-,VALGRIND_OPTS,-default-' '_valgrind'
'-value-,WWW_HOME,-default-' '_urls'
'-value-,XML_CATALOG_FILES,-default-' '_xmlsoft'
'-value-,XZ_DEFAULTS,-default-' '_xz'
'-value-,XZ_OPT,-default-' '_xz'
'-vared-' '_in_vared'
'vared' '_vared'
'varlinkctl' '_varlinkctl'
'vboxheadless' '_virtualbox'
'VBoxHeadless' '_virtualbox'
'vboxmanage' '_virtualbox'
'VBoxManage' '_virtualbox'
'vcs_info_hookadd' '_vcs_info'
'vcs_info_hookdel' '_vcs_info'
'vi' '_vi'
'view' '_vi'
'vim' '_vim'
'vimdiff' '_vim'
'virsh' '_libvirt'
'virt-admin' '_libvirt'
'virt-host-validate' '_libvirt'
'virt-pki-validate' '_libvirt'
'virt-xml-validate' '_libvirt'
'visudo' '_visudo'
'vitrage' '_openstack'
'vmstat' '_vmstat'
'vncserver' '_vnc'
'vncviewer' '_vnc'
'vnstat' '_vnstat'
'vorbiscomment' '_vorbis'
'vpnc' '_vpnc'
'vpnc-connect' '_vpnc'
'vserver' '_vserver'
'w' '_w'
'w3m' '_w3m'
'wait' '_wait'
'watch' '_watch'
'watcher' '_openstack'
'wc' '_wc'
'wemux' '_wemux'
'wget' '_wget'
'wg-quick' '_wg-quick'
'what' '_sccs'
'whatis' '_man'
'whence' '_which'
'where' '_which'
'whereis' '_whereis'
'which' '_which'
'who' '_who'
'whoami' '_nothing'
'whois' '_whois'
'whom' '_mh'
'wifi-menu' '_netctl'
'wiggle' '_wiggle'
'windsurf-next' '_windsurf-next'
'wipefs' '_wipefs'
'wl-copy' '_wl-copy'
'wl-paste' '_wl-paste'
'wodim' '_cdrecord'
'wpa_cli' '_wpa_cli'
'wpctl' '_wpctl'
'write' '_users_on'
'www' '_webbrowser'
'xargs' '_xargs'
'xattr' '_attr'
'xauth' '_xauth'
'xautolock' '_xautolock'
'xclip' '_xclip'
'xdpyinfo' '_x_utils'
'xdvi' '_xdvi'
'xelatex' '_tex'
'xetex' '_tex'
'xev' '_x_utils'
'xfd' '_x_utils'
'xfig' '_xfig'
'xfontsel' '_x_utils'
'xfreerdp' '_rdesktop'
'xhost' '_x_utils'
'xinput' '_xinput'
'xkill' '_x_utils'
'xli' '_xloadimage'
'xloadimage' '_xloadimage'
'xlsatoms' '_x_utils'
'xlsclients' '_x_utils'
'xml' '_xmlstarlet'
'xmllint' '_xmlsoft'
'xmlstarlet' '_xmlstarlet'
'xmms2' '_xmms2'
'xmodmap' '_xmodmap'
'xmosaic' '_webbrowser'
'xon' '_x_utils'
'xournal' '_xournal'
'xpdf' '_xpdf'
'xping' '_hosts'
'xprop' '_x_utils'
'xrandr' '_xrandr'
'xrdb' '_x_utils'
'xscreensaver-command' '_xscreensaver'
'xsel' '_xsel'
'xset' '_xset'
'xsetbg' '_xloadimage'
'xsetroot' '_x_utils'
'xsltproc' '_xmlsoft'
'xterm' '_xterm'
'xtightvncviewer' '_vnc'
'xtp' '_imagemagick'
'xv' '_xv'
'xview' '_xloadimage'
'xvnc4viewer' '_vnc'
'xvncviewer' '_vnc'
'xwd' '_x_utils'
'xwininfo' '_x_utils'
'xwit' '_xwit'
'xwud' '_x_utils'
'xxd' '_xxd'
'xz' '_xz'
'xzcat' '_xz'
'yafc' '_yafc'
'yarn' '_yarn'
'yash' '_sh'
'yay' '_yay'
'yazi' '_yazi'
'yfm' '_yfm'
'ypbind' '_yp'
'ypcat' '_yp'
'ypmatch' '_yp'
'yppasswd' '_yp'
'yppoll' '_yp'
'yppush' '_yp'
'ypserv' '_yp'
'ypset' '_yp'
'ypwhich' '_yp'
'ypxfr' '_yp'
'ytalk' '_other_accounts'
'zargs' '_zargs'
'zathura' '_zathura'
'zcalc' '_zcalc'
'-zcalc-line-' '_zcalc_line'
'zcash-cli' '_zcash-cli'
'zcat' '_zcat'
'zcompile' '_zcompile'
'zcp' '_zmv'
'zdb' '_zfs'
'zdelattr' '_zattr'
'zdump' '_zdump'
'zeal' '_zeal'
'zed' '_zed'
'zegrep' '_grep'
'zellij' '_zellij'
'zen' '_webbrowser'
'zf_chgrp' '_chown'
'zf_chmod' '_chmod'
'zf_chown' '_chown'
'zfgrep' '_grep'
'zf_ln' '_ln'
'zf_mkdir' '_mkdir'
'zf_mv' '_mv'
'zf_rm' '_rm'
'zf_rmdir' '_rmdir'
'zfs' '_zfs'
'zgetattr' '_zattr'
'zgrep' '_grep'
'zinit' '_zinit'
'zip' '_zip'
'zipinfo' '_zip'
'zle' '_zle'
'zlistattr' '_zattr'
'zln' '_zmv'
'zmail' '_mail'
'zmodload' '_zmodload'
'zmv' '_zmv'
'zone' '_hosts'
'zparseopts' '_zparseopts'
'zpool' '_zfs'
'zpty' '_zpty'
'zsetattr' '_zattr'
'zsh' '_zsh'
'zsh-mime-handler' '_zsh-mime-handler'
'zsocket' '_zsocket'
'zstat' '_stat'
'zstyle' '_zstyle'
'ztodo' '_ztodo'
'zun' '_openstack'
'zxpdf' '_xpdf'
)

_services=(
'bzcat' 'bunzip2'
'gchgrp' 'chgrp'
'gchown' 'chown'
'gnupod_addsong.pl' 'gnupod_addsong'
'gnupod_check.pl' 'gnupod_check'
'gnupod_INIT.pl' 'gnupod_INIT'
'gnupod_search.pl' 'gnupod_search'
'gpg2' 'gpg'
'gzcat' 'gunzip'
'https' 'http'
'iceweasel' 'firefox'
'lzcat' 'unxz'
'lzma' 'xz'
'Mail' 'mail'
'mailx' 'mail'
'mktunes.pl' 'mktunes'
'nail' 'mail'
'ncl' 'nc'
'nedit-nc' 'nc'
'pacman.static' 'pacman'
'pcat' 'unpack'
'-redirect-,<,bunzip2' 'bunzip2'
'-redirect-,<,bzip2' 'bzip2'
'-redirect-,>,bzip2' 'bunzip2'
'-redirect-,<,compress' 'compress'
'-redirect-,>,compress' 'uncompress'
'-redirect-,<,gunzip' 'gunzip'
'-redirect-,<,gzip' 'gzip'
'-redirect-,>,gzip' 'gunzip'
'-redirect-,<,uncompress' 'uncompress'
'-redirect-,<,unxz' 'unxz'
'-redirect-,<,xz' 'xz'
'-redirect-,>,xz' 'unxz'
'remsh' 'rsh'
'slogin' 'ssh'
'svnadmin-static' 'svnadmin'
'svnlite' 'svn'
'svnliteadmin' 'svnadmin'
'tunes2pod.pl' 'tunes2pod'
'unlzma' 'unxz'
'vboxheadless' 'vboxheadless'
'VBoxHeadless' 'vboxheadless'
'vboxmanage' 'vboxmanage'
'VBoxManage' 'vboxmanage'
'xelatex' 'latex'
'xetex' 'tex'
'xzcat' 'unxz'
'zf_chgrp' 'chgrp'
'zf_chown' 'chown'
)

_patcomps=(
'*/(init|rc[0-9S]#).d/*' '_init_d'
)

_postpatcomps=(
'_*' '_compadd'
'c++-*' '_gcc'
'g++-*' '_gcc'
'gcc-*' '_gcc'
'gem[0-9.]#' '_gem'
'lua[0-9.-]##' '_lua'
'(p[bgpn]m*|*top[bgpn]m)' '_pbm'
'php[0-9.-]' '_php'
'pip[0-9.]#' '_pip'
'pydoc[0-9.]#' '_pydoc'
'python[0-9.]#' '_python'
'qemu(|-system-*)' '_qemu'
'rmlint.*.sh' '_rmlint'
'(ruby|[ei]rb)[0-9.]#' '_ruby'
'shasum(|5).*' '_shasum'
'(texi(2*|ndex))' '_texi'
'(tiff*|*2tiff|pal2rgb)' '_tiff'
'-value-,CCACHE_*,-default-' '_ccache'
'-value-,CGO*,-default-' '_golang'
'-value-,(ftp|http(|s))_proxy,-default-' '_urls'
'-value-,GO*,-default-' '_golang'
'-value-,LC_*,-default-' '_locales'
'-value-,*path,-default-' '_directories'
'-value-,*PATH,-default-' '_dir_list'
'-value-,RUBY(LIB|OPT|PATH),-default-' '_ruby'
'*/X11(|R<4->)/*' '_x_arguments'
'yodl(|2*)' '_yodl'
'zf*' '_zftp'
)

_compautos=(
'_call_program' '+X'
)

zle -C _bash_complete-word .complete-word _bash_completions
zle -C _bash_list-choices .list-choices _bash_completions
zle -C _complete_debug .complete-word _complete_debug
zle -C _complete_help .complete-word _complete_help
zle -C _complete_tag .complete-word _complete_tag
zle -C _correct_filename .complete-word _correct_filename
zle -C _correct_word .complete-word _correct_word
zle -C _expand_alias .complete-word _expand_alias
zle -C _expand_word .complete-word _expand_word
zle -C _history-complete-newer .complete-word _history_complete_word
zle -C _history-complete-older .complete-word _history_complete_word
zle -C _list_expansions .list-choices _expand_word
zle -C _most_recent_file .complete-word _most_recent_file
zle -C _next_tags .list-choices _next_tags
zle -C _read_comp .complete-word _read_comp
bindkey '^X^R' _read_comp
bindkey '^X?' _complete_debug
bindkey '^XC' _correct_filename
bindkey '^Xa' _expand_alias
bindkey '^Xc' _correct_word
bindkey '^Xd' _list_expansions
bindkey '^Xe' _expand_word
bindkey '^Xh' _complete_help
bindkey '^Xm' _most_recent_file
bindkey '^Xn' _next_tags
bindkey '^Xt' _complete_tag
bindkey '^X~' _bash_list-choices
bindkey '^[,' _history-complete-newer
bindkey '^[/' _history-complete-older
bindkey '^[~' _bash_complete-word

autoload -Uz _afew _age _android _archlinux-java _artisan \
            _atach _avdmanager _bitcoin-cli _bower _bundle \
            _cap _cask _ccache _cf _chatblade \
            _choc _chromium _clang-check _clang-format _clang-tidy \
            _cmake _coffee _conan _concourse _console \
            _cppcheck _dad _dart _dget _dhcpcd \
            _diana _direnv _docpad _do-release-upgrade _drush \
            _ecdsautil _emacs _emacsclient _emulator _envdir \
            _exportfs _fab _fail2ban-client _fast-theme _ffind \
            _fleetctl _flutter _fwupdmgr _fzf _gas \
            _ghc _gist _git-flow _git-pulls _git-revise \
            _git-wtf _glances _golang _google _gpgconf \
            _grpcurl _gtk-launch _hello _hledger _homestead \
            _httpie _ibus _include-what-you-use _inxi _jest \
            _jmeter _jmeter-plugins _jonas _jrnl _kak \
            _kitchen _knife _l3build _language_codes _lilypond \
            _lscpu _lunchy _mc _middleman _mina \
            _mix _mkcert _mssh _mussh _mvn \
            _nano _nanoc _neo _neofetch _networkQuality \
            _nftables _node _nvm _openssl _openvpn3 \
            _optirun _patool _periscope _pgsql_utils _phing \
            _pixz _pkcon _play _pm2 _port \
            _pre-commit _protoc _pygmentize _qmk _rails \
            _ralio _rdfind _redis-cli _rfkill _rkt \
            _rmlint _rslsync _rspec _rsvm _rubocop \
            _sbt _scala _screencapture _scrub _sdd \
            _sdkmanager _setcap _sfdx _shallow-backup _shellcheck \
            _showoff _srm _stack _subliminal _supervisorctl \
            _supervisord _svm _teamocil _thor _tmuxinator \
            _tmuxp _tox _tsc _ts-node _udisksctl \
            _ufw _uuidgen _virtualbox _vnstat _wemux \
            _wg-quick _xsel _yarn _yfm _zcash-cli \
            _zinit _afew _alacritty _android _archlinux-java \
            _artisan _atach _atuin _avdmanager _bat \
            _bitcoin-cli _bluetoothctl _bootctl _bower _bundle \
            _busctl _bwrap _cap _cargo _cargo-tauri \
            _cask _ccache _cf _checkupdates _choc \
            _chromium _chwd _clang-check _clang-format _clang-tidy \
            _cmake _code _coffee _conan _concourse \
            _console _coredumpctl _cppcheck _ctr _curl \
            _dad _dart _dget _dhcpcd _diana \
            _direnv _docker _docpad _drush _ecdsautil \
            _emacs _emacsclient _emulator _envdir _exportfs \
            _eza _fab _fail2ban-client _fastfetch _fd \
            _ffind _flatpak _fleetctl _flutter _foot \
            _footclient _fvm _fwupdmgr _gas _gh \
            _ghc _gist _git-flow _git-pulls _git-revise \
            _git-wtf _glances _golang _google _gpgconf \
            _grpcurl _gtk-launch _hello _hledger _homestead \
            _hostnamectl _httpie _hyprctl _hyprpm _ibus \
            _img2sixel _include-what-you-use _insmod _inxi _jmeter \
            _jmeter-plugins _jonas _journalctl _jrnl _kak \
            _kdeconnect _kernel-install _keyring _kitchen _kitty \
            _knife _language_codes _libinput _lilypond _localectl \
            _loginctl _lsmod _lunchy _machinectl _mc \
            _meson _middleman _mina _mix _mkcert \
            _mkinitcpio _mpv _mssh _mussh _mvn \
            _nano _nanoc _neofetch _netctl _networkctl \
            _networkQuality _nftables _ninja _node _nvm \
            _oomctl _openssl _openvpn3 _optirun _paccache \
            _pacdiff _paclist _paclog-pkglist _pacman _pacscripts \
            _pacsearch _pacsort _pactree _parallel _paru \
            _patool _periscope _pgsql_utils _phing _pip \
            _pixz _pkcon _pkgfile _play _pm2 \
            _poetry _port _powerprofilesctl _protoc _psd \
            _pulseaudio _pygmentize _pypr _qmk _qpdf \
            _rails _ralio _rankmirrors _redis-cli _resolvectl \
            _rfkill _rg _rkt _rmlint _rmmod \
            _rslsync _rspec _rsvm _rubocop _run0 \
            _rustup _sbt _scala _scrcpy _screencapture \
            _scrub _sd _sdd _sd_hosts_or_user_at_host _sdkmanager \
            _sd_machines _sd_outputmodes _sd_unit_files _setcap _setup.py \
            _sfdx _shellcheck _showoff _srm _stack \
            _starship _subliminal _supervisorctl _svm _swww \
            _systemctl _systemd _systemd-analyze _systemd-delta _systemd-inhibit \
            _systemd-nspawn _systemd-path _systemd-run _systemd-tmpfiles _teamocil \
            _thor _timedatectl _tldr _tmuxinator _tox \
            _tsc _ts-node _udevadm _udisks2 _udisksctl \
            _ufw _updpkgsums _upower _uv _varlinkctl \
            _virtualbox _vnstat _wemux _wg-quick _windsurf-next \
            _wl-copy _wl-paste _wpctl _xsel _yarn \
            _yay _yazi _zathura _zcash-cli _zellij \
            _cdr _all_labels _all_matches _alternative _approximate \
            _arg_compile _arguments _bash_completions _cache_invalid _call_function \
            _combination _complete _complete_debug _complete_help _complete_help_generic \
            _complete_tag _comp_locale _correct _correct_filename _correct_word \
            _describe _description _dispatch _expand _expand_alias \
            _expand_word _extensions _external_pwds _generic _guard \
            _history _history_complete_word _ignored _list _main_complete \
            _match _menu _message _most_recent_file _multi_parts \
            _next_label _next_tags _normal _nothing _numbers \
            _oldlist _pick_variant _prefix _read_comp _regex_arguments \
            _regex_words _requested _retrieve_cache _sep_parts _sequence \
            _set_command _setup _store_cache _sub_commands _tags \
            _user_expand _values _wanted _acpi _acpitool \
            _alsa-utils _analyseplugin _basenc _brctl _btrfs \
            _capabilities _chattr _chcon _choom _chrt \
            _cpupower _cryptsetup _dkms _e2label _ethtool \
            _findmnt _free _fuse_arguments _fusermount _fuse_values \
            _gpasswd _htop _iconvconfig _ionice _ipset \
            _iptables _iwconfig _kpartx _losetup _lsattr \
            _lsblk _lsns _lsusb _ltrace _mat \
            _mat2 _mdadm _mii-tool _modutils _mondo \
            _networkmanager _nsenter _opkg _perf _pidof \
            _pmap _qdbus _schedtool _selinux_contexts _selinux_roles \
            _selinux_types _selinux_users _setpriv _setsid _slabtop \
            _ss _sshfs _strace _sysstat _tload \
            _tpb _tracepath _tune2fs _uml _unshare \
            _valgrind _vserver _wakeup_capable_devices _wipefs _wpa_cli \
            _a2ps _aap _abcde _absolute_command_paths _ack \
            _adb _ansible _ant _antiword _apachectl \
            _apm _arch_archives _arch_namespace _arp _arping \
            _asciidoctor _asciinema _at _attr _augeas \
            _avahi _awk _base64 _basename _bash \
            _baudrates _baz _beep _bibtex _bind_addresses \
            _bison _bittorrent _bogofilter _bpf_filters _bpython \
            _bzip2 _bzr _cabal _cal _calendar \
            _canonical_paths _cat _ccal _cdcd _cdrdao \
            _cdrecord _chkconfig _chmod _chown _chroot \
            _chsh _cksum _clay _cmdambivalent _cmdstring \
            _cmp _column _comm _composer _compress \
            _configure _cowsay _cp _cpio _cplay \
            _crontab _cscope _csplit _cssh _ctags \
            _ctags_tags _curl _cut _cvs _darcs \
            _date _date_formats _dates _dbus _dconf \
            _dd _devtodo _df _dhclient _dict \
            _dict_words _diff _diff3 _diff_options _diffstat \
            _dig _directories _dir_list _django _dmesg \
            _dmidecode _dns_types _doas _domains _dos2unix \
            _drill _dropbox _dsh _dtruss _du \
            _dvi _ecasound _ed _elfdump _elinks \
            _email_addresses _enscript _entr _env _espeak \
            _etags _fakeroot _feh _fetchmail _ffmpeg \
            _figlet _file_modes _files _file_systems _find \
            _find_net_interfaces _finger _flac _flex _fmt \
            _fold _fortune _fsh _fuser _gcc \
            _gcore _gdb _gem _genisoimage _getconf \
            _getent _getfacl _getmail _getopt _ghostscript \
            _git _global _global_tags _gnu_generic _gnupod \
            _gnutls _go _gpg _gphoto2 _gprof \
            _gradle _graphicsmagick _grep _groff _groups \
            _growisofs _gsettings _guilt _gzip _have_glob_qual \
            _head _hexdump _host _hostname _hosts \
            _iconv _id _ifconfig _iftop _imagemagick \
            _initctl _init_d _install _iostat _ip \
            _ipsec _irssi _ispell _java _java_class \
            _joe _join _jq _killall _knock \
            _kvno _last _ldconfig _ldd _ld_debug \
            _less _lha _libvirt _links _list_files \
            _lldb _ln _loadkeys _locale _localedef \
            _locales _locate _logger _look _lp \
            _ls _lsof _lua _luarocks _lynx \
            _lz4 _lzop _mail _mailboxes _make \
            _man _md5sum _mencal _mh _mime_types \
            _mkdir _mkfifo _mknod _mktemp _module \
            _monotone _moosic _mosh _mount _mpc \
            _mt _mtools _mtr _mutt _mv \
            _my_accounts _myrepos _mysqldiff _mysql_utils _ncftp \
            _netcat _net_interfaces _netstat _newsgroups _nginx \
            _ngrep _nice _nkf _nl _nm \
            _nmap _npm _nslookup _numfmt _objdump \
            _object_files _od _openstack _opustools _other_accounts \
            _pack _pandoc _paste _patch _patchutils \
            _path_commands _path_files _pax _pbm _pdf \
            _perforce _perl _perl_basepods _perldoc _perl_modules \
            _pgids _pgrep _php _picocom _pids \
            _pine _ping _pip _pkgadd _pkg-config \
            _pkginfo _pkg_instance _pkgrm _pon _ports \
            _postfix _postgresql _postscript _pr _printenv \
            _printers _process_names _prove _ps _pspdf \
            _psutils _ptx _pump _pv _pwgen \
            _pydoc _python _python_modules _qemu _quilt \
            _rake _ranlib _rar _rclone _rcs \
            _readelf _readlink _remote_files _renice _ri \
            _rlogin _rm _rmdir _route _rrdtool \
            _rsync _rubber _ruby _runit _samba \
            _sccs _scons _screen _script _seafile \
            _sed _seq _service _services _setfacl \
            _sh _shasum _showmount _shred _shuf \
            _shutdown _signals _sisu _slrn _smartmontools \
            _socket _sort _spamassassin _split _sqlite \
            _sqsh _ssh _ssh_hosts _stat _stdbuf \
            _stgit _stow _strings _strip _stty \
            _su _subversion _sudo _surfraw _swaks \
            _swanctl _swift _sys_calls _sysctl _tac \
            _tail _tar _tar_archive _tardy _tcpdump \
            _tcptraceroute _tee _telnet _terminals _tex \
            _texi _texinfo _tidy _tiff _tilde_files \
            _timeout _time_zone _tin _tla _tmux \
            _todo.sh _toilet _top _topgit _totd \
            _touch _tput _tr _transmission _tree \
            _truncate _truss _tty _ttys _twidge \
            _twisted _umountable _unace _uname _unexpand \
            _uniq _unison _units _uptime _urls \
            _user_admin _user_at_host _users _users_on _vi \
            _vim _visudo _vmstat _vorbis _vpnc \
            _w _w3m _watch _wc _webbrowser \
            _wget _whereis _who _whois _wiggle \
            _xargs _xmlsoft _xmlstarlet _xmms2 _xxd \
            _xz _yafc _yodl _yp _zcat \
            _zdump _zfs _zfs_dataset _zfs_pool _zip \
            _zsh _acroread _code _dcop _eog \
            _evince _geany _gnome-gv _gqview _gv \
            _kdeconnect _kfmclient _matlab _mozilla _mplayer \
            _mupdf _nautilus _nedit _netscape _okular \
            _pdftk _qiv _rdesktop _setxkbmap _sublimetext \
            _urxvt _vnc _x_arguments _xauth _xautolock \
            _x_borderwidth _xclip _x_color _x_colormapid _x_cursor \
            _x_display _xdvi _x_extension _xfig _x_font \
            _xft_fonts _x_geometry _xinput _x_keysym _xloadimage \
            _x_locale _x_modifier _xmodmap _x_name _xournal \
            _xpdf _xrandr _x_resource _xscreensaver _x_selection_timeout \
            _xset _xt_arguments _xterm _x_title _xt_session_id \
            _x_utils _xv _x_visual _x_window _xwit \
            _zeal _add-zle-hook-widget _add-zsh-hook _alias _aliases \
            __arguments _arrays _assign _autocd _bindkey \
            _brace_parameter _builtin _cd _command _command_names \
            _compadd _compdef _completers _condition _default \
            _delimiters _directory_stack _dirs _disable _dynamic_directory_name \
            _echotc _echoti _emulate _enable _equal \
            _exec _fc _file_descriptors _first _functions \
            _globflags _globqual_delims _globquals _hash _history_modifiers \
            _in_vared _jobs _jobs_bg _jobs_builtin _jobs_fg \
            _kill _limit _limits _math _math_params \
            _mere _module_math_func _options _options_set _options_unset \
            _parameter _parameters _precommand _print _prompt \
            _ps1234 _read _redirect _run-help _sched \
            _set _setopt _source _strftime _subscript \
            _suffix_alias_files _tcpsys _tilde _trap _ttyctl \
            _typeset _ulimit _unhash _user_math_func _value \
            _vared _vars _vcs_info _vcs_info_hooks _wait \
            _which _widgets _zargs _zattr _zcalc \
            _zcalc_line _zcompile _zed _zftp _zle \
            _zmodload _zmv _zparseopts _zpty _zsh-mime-handler \
            _zsocket _zstyle _ztodo _afew _alacritty \
            _android _archlinux-java _artisan _atach _atuin \
            _avdmanager _bat _bitcoin-cli _bluetoothctl _bootctl \
            _bower _bundle _busctl _bwrap _cap \
            _cargo _cargo-tauri _cask _ccache _cf \
            _checkupdates _choc _chromium _chwd _clang-check \
            _clang-format _clang-tidy _cmake _code _coffee \
            _conan _concourse _console _coredumpctl _cppcheck \
            _ctr _curl _dad _dart _dget \
            _dhcpcd _diana _direnv _docker _docpad \
            _drush _ecdsautil _emacs _emacsclient _emulator \
            _envdir _exportfs _eza _fab _fail2ban-client \
            _fastfetch _fd _ffind _flatpak _fleetctl \
            _flutter _foot _footclient _fvm _fwupdmgr \
            _gas _gh _ghc _gist _git-flow \
            _git-pulls _git-revise _git-wtf _glances _golang \
            _google _gpgconf _grpcurl _gtk-launch _hello \
            _hledger _homestead _hostnamectl _httpie _hyprctl \
            _hyprpm _ibus _img2sixel _include-what-you-use _insmod \
            _inxi _jmeter _jmeter-plugins _jonas _journalctl \
            _jrnl _kak _kdeconnect _kernel-install _keyring \
            _kitchen _kitty _knife _language_codes _libinput \
            _lilypond _localectl _loginctl _lsmod _lunchy \
            _machinectl _mc _meson _middleman _mina \
            _mix _mkcert _mkinitcpio _mpv _mssh \
            _mussh _mvn _nano _nanoc _neofetch \
            _netctl _networkctl _networkQuality _nftables _ninja \
            _node _nvm _oomctl _openssl _openvpn3 \
            _optirun _paccache _pacdiff _paclist _paclog-pkglist \
            _pacman _pacscripts _pacsearch _pacsort _pactree \
            _parallel _paru _patool _periscope _pgsql_utils \
            _phing _pip _pixz _pkcon _pkgfile \
            _play _pm2 _poetry _port _powerprofilesctl \
            _protoc _psd _pulseaudio _pygmentize _pypr \
            _qmk _qpdf _rails _ralio _rankmirrors \
            _redis-cli _resolvectl _rfkill _rg _rkt \
            _rmlint _rmmod _rslsync _rspec _rsvm \
            _rubocop _run0 _rustup _sbt _scala \
            _scrcpy _screencapture _scrub _sd _sdd \
            _sd_hosts_or_user_at_host _sdkmanager _sd_machines _sd_outputmodes _sd_unit_files \
            _setcap _setup.py _sfdx _shellcheck _showoff \
            _srm _stack _starship _subliminal _supervisorctl \
            _svm _swww _systemctl _systemd _systemd-analyze \
            _systemd-delta _systemd-inhibit _systemd-nspawn _systemd-path _systemd-run \
            _systemd-tmpfiles _teamocil _thor _timedatectl _tldr \
            _tmuxinator _tox _tsc _ts-node _udevadm \
            _udisks2 _udisksctl _ufw _updpkgsums _upower \
            _uv _varlinkctl _virtualbox _vnstat _wemux \
            _wg-quick _windsurf-next _wl-copy _wl-paste _wpctl \
            _xsel _yarn _yay _yazi _zathura \
            _zcash-cli _zellij _afew _alacritty _android \
            _archlinux-java _artisan _atach _atuin _avdmanager \
            _bat _bitcoin-cli _bluetoothctl _bootctl _bower \
            _bundle _busctl _bwrap _cap _cargo \
            _cargo-tauri _cask _ccache _cf _checkupdates \
            _choc _chromium _chwd _clang-check _clang-format \
            _clang-tidy _cmake _code _coffee _conan \
            _concourse _console _coredumpctl _cppcheck _ctr \
            _curl _dad _dart _dget _dhcpcd \
            _diana _direnv _docker _docpad _drush \
            _ecdsautil _emacs _emacsclient _emulator _envdir \
            _exportfs _eza _fab _fail2ban-client _fastfetch \
            _fd _ffind _flatpak _fleetctl _flutter \
            _foot _footclient _fvm _fwupdmgr _gas \
            _gh _ghc _gist _git-flow _git-pulls \
            _git-revise _git-wtf _glances _golang _google \
            _gpgconf _grpcurl _gtk-launch _hello _hledger \
            _homestead _hostnamectl _httpie _hyprctl _hyprpm \
            _ibus _img2sixel _include-what-you-use _insmod _inxi \
            _jmeter _jmeter-plugins _jonas _journalctl _jrnl \
            _kak _kdeconnect _kernel-install _keyring _kitchen \
            _kitty _knife _language_codes _libinput _lilypond \
            _localectl _loginctl _lsmod _lunchy _machinectl \
            _mc _meson _middleman _mina _mix \
            _mkcert _mkinitcpio _mpv _mssh _mussh \
            _mvn _nano _nanoc _neofetch _netctl \
            _networkctl _networkQuality _nftables _ninja _node \
            _nvm _oomctl _openssl _openvpn3 _optirun \
            _paccache _pacdiff _paclist _paclog-pkglist _pacman \
            _pacscripts _pacsearch _pacsort _pactree _parallel \
            _paru _patool _periscope _pgsql_utils _phing \
            _pip _pixz _pkcon _pkgfile _play \
            _pm2 _poetry _port _powerprofilesctl _protoc \
            _psd _pulseaudio _pygmentize _pypr _qmk \
            _qpdf _rails _ralio _rankmirrors _redis-cli \
            _resolvectl _rfkill _rg _rkt _rmlint \
            _rmmod _rslsync _rspec _rsvm _rubocop \
            _run0 _rustup _sbt _scala _scrcpy \
            _screencapture _scrub _sd _sdd _sd_hosts_or_user_at_host \
            _sdkmanager _sd_machines _sd_outputmodes _sd_unit_files _setcap \
            _setup.py _sfdx _shellcheck _showoff _srm \
            _stack _starship _subliminal _supervisorctl _svm \
            _swww _systemctl _systemd _systemd-analyze _systemd-delta \
            _systemd-inhibit _systemd-nspawn _systemd-path _systemd-run _systemd-tmpfiles \
            _teamocil _thor _timedatectl _tldr _tmuxinator \
            _tox _tsc _ts-node _udevadm _udisks2 \
            _udisksctl _ufw _updpkgsums _upower _uv \
            _varlinkctl _virtualbox _vnstat _wemux _wg-quick \
            _windsurf-next _wl-copy _wl-paste _wpctl _xsel \
            _yarn _yay _yazi _zathura _zcash-cli \
            _zellij
autoload -Uz +X _call_program

typeset -gUa _comp_assocs
_comp_assocs=( '' )
