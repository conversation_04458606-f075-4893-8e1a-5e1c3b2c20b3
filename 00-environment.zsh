# Environment Variables

export PATH="$HOME/bin:$PATH"
export PATH="$HOME/.local/share/cargo/bin:$PATH"
export GEMINI_API_KEY="AIzaSyABxksvqQOhy8WVaNw8Ic5lIDpPTmP602U"
export HSA_OVERRIDE_GFX_VERSION="10.3.0"
export HCC_AMDGPU_TARGET="gfx1151"
export PERPLEXITY_API_KEY="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"
export OPENROUTER_API_KEY="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"
export OPENAI_API_KEY="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"
export OPENAI_API_BASE="https://openrouter.ai/api/v1"

# MCP Server API Keys
export EXA_API_KEY="05c63900-ea45-420e-ba61-3d2e134c575a"
export SMITHERY_API_KEY="6c2a0e39-4c52-4c80-abe5-76e49d67b64c"
export GITHUB_PERSONAL_ACCESS_TOKEN="****************************************"
export BRAVE_API_KEY="BSAPoRkqZO9tOX6ZgicdO0Ds298snm7"
export STEEL_API_KEY="ste-CG3V13kXw7sYZNQHMwlOuCE6AJkJDJ7Xp6EMkuHc4OOGBSdfECiOFmO7vLdZVdyxLsmTrtyu6GL8FOupifDQLEFDqqRwQXhrixP"
export RAINDROP_ACCESS_TOKEN="542805ee-c1a4-4e5f-968c-69682967710f"
export API_KEY="3b8aab9a470318faea3527e0f8e43e335eac66586513f53777e1f6b63faf3abb"
export OPENAI_API_KEY="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"

# Load environment variables from ~/.config/.env
if [ -f "$HOME/.config/.env" ]; then
  source "$HOME/.config/.env"
fi

# === THEME SYSTEM INTEGRATION ===
# Fast theme system integration - resilient and snappy
THEME_SYSTEM_DIR="$HOME/.config/zsh/theme_system"

# Add theme system to PATH if it exists
if [[ -d "$THEME_SYSTEM_DIR" ]]; then
    export PATH="$THEME_SYSTEM_DIR:$PATH"

    # Fast theme aliases (no dependencies)
    if [[ -x "$THEME_SYSTEM_DIR/theme-fast" ]]; then
        alias theme-fast="$THEME_SYSTEM_DIR/theme-fast"
        alias dark="$THEME_SYSTEM_DIR/theme-fast gruvbox-dark"
        alias light="$THEME_SYSTEM_DIR/theme-fast gruvbox-light"
        alias prismatic="$THEME_SYSTEM_DIR/theme-fast prismatic-dark"
        alias catppuccin="$THEME_SYSTEM_DIR/theme-fast catppuccin-mocha"
        alias nord="$THEME_SYSTEM_DIR/theme-fast nord"
        alias theme-toggle="$THEME_SYSTEM_DIR/theme-fast toggle"
        alias themes="$THEME_SYSTEM_DIR/theme-fast list"
        alias theme-switch="pypr toggle theme-switcher"
    fi

    # Full theme system aliases (with dependencies)
    if [[ -x "$THEME_SYSTEM_DIR/theme" ]]; then
        alias theme-full="$THEME_SYSTEM_DIR/theme"
        alias theme-select="$THEME_SYSTEM_DIR/theme select"
        alias theme-create="$THEME_SYSTEM_DIR/theme create"
        alias theme-status="$THEME_SYSTEM_DIR/theme status"
    fi
fi

# Auto-apply current theme on startup (fast and silent)
# Temporarily disabled to fix foot crashes
# if [[ -x "$THEME_SYSTEM_DIR/theme-fast" ]]; then
#     # Only reload if we're in an interactive shell and not already themed
#     if [[ $- == *i* ]] && [[ -z "$THEME_APPLIED" ]]; then
#         export THEME_APPLIED=1
#         "$THEME_SYSTEM_DIR/theme-fast" "$(cat ~/.config/current-theme 2>/dev/null || echo gruvbox-dark)" >/dev/null 2>&1 &
#     fi
# fi
