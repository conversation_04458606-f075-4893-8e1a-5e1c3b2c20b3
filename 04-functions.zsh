# Minimal Functions for convenience
function update-system() {
  echo "Updating Arch Linux system..."
  sudo pacman -Syu --noconfirm
  yay -Syu --noconfirm
  echo "System update complete."
}

function backup-configs() {
  local backup_dir="~/backups/configs/$(date +%Y%m%d_%H%M%S)"
  mkdir -p $backup_dir
  echo "Backing up configurations to $backup_dir..."
  cp -r ~/.zshrc ~/.zimrc ~/.config/zellij $backup_dir
  echo "Backup complete."
}

function git-config-push() {
  local repo_dir="~/dotfiles"
  if [ -d "$repo_dir" ]; then
    echo "Syncing dotfiles to git repository..."
    cp ~/.zshrc ~/.zimrc $repo_dir/
    cp -r ~/.config/zellij $repo_dir/
    cd $repo_dir
    git add .
    git commit -m "Update dotfiles - $(date +%Y%m%d_%H%M%S)"
    git push origin main
    echo "Dotfiles pushed to repository."
  else
    echo "Dotfiles repository not found at $repo_dir. Please initialize it first."
  fi
}

# 'plx' alias for launching CLI with 'uv' scripting and Python environment handling
function plx() {
  # Check if first argument is a Python file
  if [[ "$1" == *.py ]]; then
    # Python environment handling
    if [ -d .venv ] && [ -f .venv/bin/activate ]; then
      source .venv/bin/activate || { echo 'Error: Failed to activate virtual environment' >&2; return 1; }
      echo "Activated Python environment from .venv"
    elif [ -d venv ] && [ -f venv/bin/activate ]; then
      source venv/bin/activate || { echo 'Error: Failed to activate virtual environment' >&2; return 1; }
      echo "Activated Python environment from venv"
    fi

    # Run Python script with uv instead of python directly
    if command -v uv > /dev/null 2>&1; then
      uv run "$@" || { echo 'Error running Python script with uv. Check file and permissions.' >&2; return 1; }
    else
      echo 'Error: uv not installed. Install via pip install uv' >&2
      return 1
    fi
  # Original CLI launching functionality
  elif [ -f /home/<USER>/.config/tempo/cli.sh ]; then
    uv /home/<USER>/.config/tempo/cli.sh "$@"  # Use uv for scripting
  else
    # Try to run any Python script in tempo directory if cli.sh doesn't exist
    if [ -f /home/<USER>/.config/tempo/cli.py ]; then
      uv run /home/<USER>/.config/tempo/cli.py "$@" || { echo 'Error running CLI script' >&2; return 1; }
    else
      echo 'Error: CLI script not found in /home/<USER>/.config/tempo' >&2
      return 1
    fi
  fi
}
alias plx=plx

# Docker functions with comprehensive error handling
function docker-add() {
  if ! command -v docker > /dev/null 2>&1; then echo 'Error: Docker not installed' >&2; return 1; fi
  if ! docker info > /dev/null 2>&1; then echo 'Error: Docker daemon not running' >&2; return 1; fi
  docker run "$@" || { echo 'Docker add failed. Check command and permissions.' >&2; return 1; }
}

function docker-status() {
  if ! command -v docker > /dev/null 2>&1; then echo 'Error: Docker not installed' >&2; return 1; fi
  if ! docker info > /dev/null 2>&1; then echo 'Error: Docker daemon not running' >&2; return 1; fi
  docker ps -a --format "table {{.ID}}\t{{.Image}}\t{{.Status}}\t{{.Names}}" | head -n 15  # Formatted output with limit
}

function docker-kill() {
  if ! command -v docker > /dev/null 2>&1; then echo 'Error: Docker not installed' >&2; return 1; fi
  if [ -z "$1" ]; then echo 'Error: Container ID or name required' >&2; return 1; fi
  docker kill "$1" || { echo "Error killing container $1" >&2; return 1; }
}

function docker-remove() {
  if ! command -v docker > /dev/null 2>&1; then echo 'Error: Docker not installed' >&2; return 1; fi
  if [ -z "$1" ]; then echo 'Error: Container ID or name required' >&2; return 1; fi
  docker rm "$1" || { echo "Error removing container $1" >&2; return 1; }
}

function docker-remove-all() {
  if ! command -v docker > /dev/null 2>&1; then echo 'Error: Docker not installed' >&2; return 1; fi
  if ! docker ps -a -q > /dev/null 2>&1; then echo 'No containers to remove' >&2; return 0; fi
  read -p 'Are you sure you want to remove all containers? [y/N] ' confirm
  if [ "$confirm" = "y" ]; then
    docker rm -f $(docker ps -a -q) || { echo 'Error removing containers' >&2; return 1; }
    echo 'All containers removed successfully'
  else
    echo 'Operation cancelled'
  fi
}

# Simplified npm function (heavy function moved to lazy loading)
function npm-knz-cfg() {
  autoload -Uz _npm_knz_cfg_full
  _npm_knz_cfg_full "$@"
}

# Git functions with similar naming convention and functionality
function git-knz-cfg() {
  # Check if git is installed
  if ! command -v git > /dev/null; then
    echo 'Error: Git not installed. Install git first.' >&2
    return 1
  fi

  # Handle different git operations
  case "$1" in
    search)
      shift
      echo "Searching git repositories for: $@"
      if command -v gh > /dev/null; then
        gh repo search "$@" --limit 5
      else
        echo "GitHub CLI not installed. Using basic search."
        git ls-remote --refs "https://github.com/search?q=$@" 2>/dev/null | head -n 5 || echo "No results found"
      fi
      ;;
    clone)
      shift
      if [ -z "$1" ]; then
        echo "Usage: git-knz-cfg clone <repository> [directory]" >&2
        return 1
      fi
      echo "Cloning repository: $1"
      git clone "$1" "${2:-}" || echo "Failed to clone repository" >&2
      ;;
    status)
      git status
      ;;
    history)
      echo "Recent git commands:"
      history | grep -i "git" | grep -v "git-knz-cfg" | tail -n 10
      ;;
    *)
      echo "Usage: git-knz-cfg [search|clone|status|history] [arguments...]" >&2
      echo "  search: Search for repositories"
      echo "  clone: Clone a repository"
      echo "  status: Show status of current repository"
      echo "  history: Show recent git commands"
      ;;
  esac
}
alias git-knz-cfg=git-knz-cfg

# Python environment handling and aliases
function load-python-env() {
  if ! command -v python3 > /dev/null 2>&1; then echo 'Error: Python not installed. Install via pacman or other package manager.' >&2; return 1; fi
  if [ -d .venv ]; then
    source .venv/bin/activate || { echo 'Error: Failed to activate virtual environment' >&2; return 1; }
  elif command -v python -m venv > /dev/null 2>&1; then
    echo 'Virtual env not found; create one with `python -m venv .venv` first.'
  else
    echo 'venv module not available; ensure Python is fully installed.' >&2
  fi
}

alias pyrun='python3 "$@" || { echo 'Error running Python script. Check file and permissions.' >&2; }'
