#!/usr/bin/env zsh
# p10k-optimized.zsh - Performance optimizations for Powerlevel10k
# This script enables advanced caching and performance features

# Enable instant prompt for fastest startup (quiet mode to suppress warnings)
typeset -g POWERLEVEL9K_INSTANT_PROMPT=quiet

# Enable advanced caching features
typeset -g POWERLEVEL9K_DISABLE_HOT_RELOAD=false

# Optimize git status performance
typeset -g POWERLEVEL9K_VCS_MAX_INDEX_SIZE_DIRTY=4096

# Cache git status for better performance
typeset -g POWERLEVEL9K_VCS_STAGED_MAX_NUM=10
typeset -g POWERLEVEL9K_VCS_UNSTAGED_MAX_NUM=10
typeset -g POWERLEVEL9K_VCS_UNTRACKED_MAX_NUM=10

# Function to clean cache only when needed (not on every startup!)
function clean_p10k_cache() {
  echo "Cleaning p10k cache files..."
  rm -f "${XDG_CACHE_HOME:-$HOME/.cache}/p10k-instant-prompt-${(%):-%n}.zsh" 2>/dev/null
  rm -f "${XDG_CACHE_HOME:-$HOME/.cache}/p10k-dump-${(%):-%n}.zsh" 2>/dev/null
  rm -f "${XDG_CACHE_HOME:-$HOME/.cache}/p10k-*" 2>/dev/null
  echo "Done. Please restart your shell or source your .zshrc file."
}

# Function to rebuild p10k cache for optimal performance
function rebuild_p10k_cache() {
  echo "Rebuilding p10k cache for optimal performance..."
  clean_p10k_cache
  echo "Cache cleared. Starting new shell to rebuild cache..."
  exec zsh
}

# Create aliases for cache management
alias p10k-clean=clean_p10k_cache
alias p10k-rebuild=rebuild_p10k_cache

# Performance tip: Only clean cache if you're experiencing issues
# Use 'p10k-clean' only if experiencing prompt issues, not for regular use
# Silent mode - no console output during initialization
