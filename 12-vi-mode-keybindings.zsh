# Vi Mode Keybindings
# Ensure shift and arrow keys work in vi mode

# Enable vi mode if not already enabled
bindkey -v

# Custom keybindings for vi mode
bindkey '^[[1;2C' forward-word     # Shift+Right Arrow
bindkey '^[[1;2D' backward-word    # Shift+Left Arrow
bindkey '^[[1;2A' up-line-or-history   # Shift+Up Arrow
bindkey '^[[1;2B' down-line-or-history # Shift+Down Arrow

# Backspace keybindings for precise and intuitive deletes
# Regular Backspace: delete single character
bindkey '^?' backward-delete-char       # Backspace (no modifier)
# Shift+Backspace: delete word (try common codes for Shift+Backspace)
bindkey '^[[3;2~' backward-kill-word    # Shift+Backspace (code 1, xterm)
bindkey '^[[1;2H' backward-kill-word    # Shift+Backspace (code 2)
bindkey '^[[H' backward-kill-word       # Shift+Backspace (code 3)
# Ctrl+Backspace: delete single character (most terminals send ^H)
bindkey '^H' backward-delete-char       # Ctrl+Backspace
# Ctrl+Shift+Backspace: delete whole line
bindkey '^[[1;5H' kill-whole-line       # Ctrl+Shift+Backspace

# Ctrl+Right Arrow to move forward by word with detailed stops (stops at word boundaries)
bindkey '^[[1;5C' forward-word        # Ctrl+Right Arrow

# Ctrl+Left Arrow to move backward by word with detailed stops (stops at word boundaries)
bindkey '^[[1;5D' backward-word       # Ctrl+Left Arrow

# Ctrl+Up Arrow to move up in history
bindkey '^[[1;5A' up-line-or-history  # Ctrl+Up Arrow

# Ctrl+Down Arrow to move down in history
bindkey '^[[1;5B' down-line-or-history # Ctrl+Down Arrow

# Alt+Up Arrow to search history backward
bindkey '^[[1;3A' history-search-backward # Alt+Up Arrow

# Alt+Down Arrow to search history forward
bindkey '^[[1;3B' history-search-forward  # Alt+Down Arrow

# Alt+Right Arrow to accept suggestion (if available)
bindkey '^[[1;3C' forward-char           # Alt+Right Arrow (placeholder for suggestion acceptance)

# Alt+Left Arrow to reject suggestion or move back (if available)
bindkey '^[[1;3D' backward-char          # Alt+Left Arrow (placeholder for suggestion navigation)

# Ensure standard arrow keys work as expected
bindkey '^[[C' forward-char        # Right Arrow
bindkey '^[[D' backward-char       # Left Arrow
bindkey '^[[A' up-line-or-history  # Up Arrow
bindkey '^[[B' down-line-or-history # Down Arrow

# Additional useful vi mode keybindings
bindkey -M vicmd 'H' beginning-of-line
bindkey -M vicmd 'L' end-of-line
