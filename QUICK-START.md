# 🚀 Quick Start Guide - Optimized Workflow

## ⚡ **Immediate Actions**

### 1. Test Your Setup
```bash
# Test startup speed (should be < 0.01s)
time zsh -i -c exit

# Test Zellij with optimized config
zellij --config ~/.config/zellij/config-optimized.kdl

# Test FZF integration
fzf --version
```

### 2. Essential Shortcuts to Learn First
| Key | Action | Why Important |
|-----|--------|---------------|
| `Ctrl+A` | Alias finder | Discover all available shortcuts |
| `Ctrl+R` | History search | Find previous commands instantly |
| `Alt+h/j/k/l` | Navigate Zellij panes | Vim-style navigation |
| `Alt+t` | New Zellij tab | Quick workspace creation |
| `Alt+n` | New Zellij pane | Split current view |

### 3. Quick Workflow Test
```bash
# 1. Open alias finder
# Press Ctrl+A and explore available commands

# 2. Navigate directories with FZF
cdf

# 3. Find files quickly
ff

# 4. Switch projects
proj

# 5. Git workflow
gs    # status
ga .  # add all
gc "test commit"  # commit
```

---

## 🎯 **Workflow Scenarios**

### Development Session
```bash
# 1. Start optimized Zellij session
zjs

# 2. Navigate to project
proj

# 3. Open files with FZF
ff

# 4. Git workflow
gb    # switch branch with FZF
gs    # check status
```

### System Administration
```bash
# 1. System update
sysup

# 2. Monitor processes
fp    # FZF process manager

# 3. Check system info
sysinfo

# 4. Clean system
sysclean
```

### File Management
```bash
# 1. Navigate directories
cdf

# 2. Find files
ff

# 3. Copy paths
cpwd  # copy current directory
```

---

## 🔧 **Customization**

### Add Your Own Aliases
Edit `~/.config/zsh/14-aliases-optimized.zsh`:
```bash
# Add to the aliases array in alias_finder_fzf()
"myalias:my command:Description of what it does"
```

### Customize Zellij Hotkeys
Edit `~/.config/zellij/config-optimized.kdl`:
```kdl
bind "Alt y" { /* your action */ }
```

### Add FZF Functions
Edit `~/.config/zsh/15-workflow-enhanced.zsh`:
```bash
my_fzf_function() {
  # Your FZF integration here
}
```

---

## 🐛 **Troubleshooting**

### Common Issues & Solutions

| Problem | Solution |
|---------|----------|
| Slow startup | `time zsh -i -c exit` should be < 0.01s |
| Alt+R error | Fixed - old alias_finder removed |
| Zellij ScrollLeft error | Fixed - using optimized config |
| FZF not working | Check `fzf --version` and install if needed |
| Git shortcuts fail | Ensure you're in a git repository |

### Performance Commands
```bash
p10k-clean      # Clean prompt cache
p10k-rebuild    # Rebuild prompt cache
reload          # Reload zsh config
alias_help      # Show all aliases
```

### Reset to Defaults
```bash
# Backup current config
cp ~/.zshrc ~/.zshrc.backup

# Use optimized Zellij config
cp ~/.config/zellij/config-optimized.kdl ~/.config/zellij/config.kdl

# Reload shell
exec zsh
```

---

## 📚 **Learning Path**

### Week 1: Basics
- [ ] Learn `Ctrl+A` for alias discovery
- [ ] Master `Alt+h/j/k/l` for navigation
- [ ] Use `Ctrl+R` for history search
- [ ] Practice basic git shortcuts: `gs`, `ga`, `gc`, `gp`

### Week 2: FZF Integration
- [ ] Use `ff` for file finding
- [ ] Use `cdf` for directory navigation
- [ ] Try `proj` for project switching
- [ ] Explore `fp` for process management

### Week 3: Advanced Features
- [ ] Learn Zellij text selection (`Alt+s`)
- [ ] Use git branch switcher (`gb`)
- [ ] Explore git log browser (`gl`)
- [ ] Customize your own shortcuts

### Week 4: Optimization
- [ ] Create custom FZF functions
- [ ] Add personal aliases
- [ ] Optimize for your specific workflow
- [ ] Share improvements with the community

---

## 🎮 **Hyprland Integration**

### Terminal Shortcuts
| Key | Action |
|-----|--------|
| `Super+Return` | Quick terminal |
| `Super+Shift+Return` | Floating terminal |
| `Super+Shift+Z` | Zsh terminal |
| `Super+Shift+J` | Zellij session |
| `Super+Shift+D` | Development terminal |

### Window Management
| Key | Action |
|-----|--------|
| `Super+h/j/k/l` | Focus window |
| `Super+1-9` | Switch workspace |
| `Super+Shift+1-9` | Move to workspace |

---

## 🌟 **Pro Tips**

### Efficiency Boosters
1. **Use Tab Completion**: Enhanced with fzf-tab for visual selection
2. **Combine Commands**: `gs && ga . && gc "quick fix"`
3. **Use Aliases**: `sysup && sysclean` for full system maintenance
4. **Project Workflow**: `proj && ff` to quickly open project files

### Keyboard-Only Workflow
1. **Never touch mouse**: All functions have keyboard shortcuts
2. **Vim-style navigation**: Consistent h/j/k/l across all tools
3. **FZF everywhere**: File, directory, process, git, session management
4. **Smart defaults**: Most commands work without arguments

### Performance Optimization
1. **Lazy loading**: Heavy functions load only when needed
2. **Smart caching**: Git status and session info cached
3. **Minimal startup**: < 0.01s startup time
4. **Efficient plugins**: Only essential plugins loaded

---

## 🔗 **Resources**

### Documentation
- `HOTKEYS.md` - Complete hotkey reference
- `README.md` - Full configuration documentation
- `alias_help` - Interactive help system

### Configuration Files
- `~/.config/zsh/` - All zsh configuration
- `~/.config/zellij/config-optimized.kdl` - Optimized Zellij config
- `~/.config/hypr/configs/binds.conf` - Hyprland key bindings

### Commands for Help
```bash
alias_help          # Show all aliases
Ctrl+A              # Interactive alias finder
man zellij          # Zellij manual
fzf --help          # FZF help
```

---

*Start with the essential shortcuts and gradually explore advanced features. The workflow is designed to be discoverable - use `Ctrl+A` whenever you need to find a command!*
